interface PageListParams {
  current: number;
  size: number;
}
interface BaseDataParams {
  // 主键
  id?: number;
  // 创建人
  createBy?: string;
  // 创建时间
  createTime?: Date | number | string;
  // 更新人
  updateBy?: string;
  // 更新时间
  updateTime?: Date | number | string;
  // 逻辑删除
  deleteFlag?: number;
  // 租户主键
  tenantId?: number;
}
interface Pagination<T> {
  current: number;
  records: T[];
  size: number;
  total: number;
}
interface ConfigInfo {
  // 企业号Id
  qyhCorpId: string;
  // 启用同步用户
  qyhSynIsSynUser: number;
  // 凭证密钥
  qyhAppSecret: string;
  // 同步密钥
  qyhCorpSecret: string;
  // 应用凭证
  qyhAgentId: string;
  // 启用同步组织
  qyhSynIsSynOrg: number;
  // 企业号Id
  dingAgentId: string;
  // 凭证密钥
  dingSynAppSecret: string;
  // 启用同步用户
  dingSynIsSynUser: number;
  // 启用同步组织
  dingSynIsSynOrg: number;
  // 应用凭证
  dingSynAppKey: string;
  // 登录方式
  loginType: string;
  // 新用户默认密码
  newUserDefaultPassword: string;
  // 包含字符串
  containsCharacters: number;
  // 密码强度限制
  passwordStrengthLimit: number;
  // 包含小写字母
  includeLowercaseLetters: number;
  // 密码最小长度
  passwordLengthMin: number;
  // 密码最小长度值
  passwordLengthMinNumber: number;
  // 包含大写字母
  includeUppercaseLetters: number;
  // 修改初始密码提醒
  mandatoryModificationOfInitialPassword: number;
  // 包含数字
  containsNumbers: number;
  // 密码错误次数
  passwordErrorsNumber: number;
  // 密码定期更新
  passwordIsUpdatedRegularly: number;
  // 禁用旧密码
  disableOldPassword: number;
  // 禁用旧密码数量
  disableNumberOfOldPasswords: number;
  // 密码更新周期（天）
  passwordUpdatedCycle: number;
  // 提前提醒天数（天）
  reminderDaysInAdvance: number;
  // 登录验证码
  verificationCodeSwitch: number;
  // 超时登录
  tokenTimeout: number;
  // 白名单验证
  whitelistSwitch: number;
  // 验证码类型
  verificationCodeType: string;
  // 错误锁定时间
  lockTime: number;
  // 白名单设置
  whitelistIp: string;
  // SSL安全连接
  emailSsl: number;
  // SMTP密码
  emailPassword: string;
  // SMTP端口
  emailSmtpPort: number;
  // 发件人昵称
  emailSenderName: string;
  // SMTP用户名
  emailAccount: string;
  // SMTP服务器
  emailSmtpHost: string;
  // Logo图标
  imgLogo: string;
  // Favicon图标
  imgFavicon: string;
  // 登录背景图
  imgLoginBackground: string;
  // 标题
  title: string;
  // 页脚信息
  copyright: string;
  // 备案号
  icpLicense: string;
  // 登录页显示
  isLoginShow: number;
  // 导航栏显示
  isNavbarShow: number;
  // 默认模型
  defaultModel: string;
}
interface DriveFileInfo {
  // 文件后缀名 (冗余字段)
  fileExtension?: string;
  // 附件ID (关联oss_attach.id，文件夹此字段为NULL)
  fileId?: number;
  // 文件/文件夹显示名称 (用户可修改)
  fileName?: string;
  // 文件大小 (单位: B, 冗余字段)
  fileSize?: number;
  // 节点类型 (0:文件夹, 1:文件)
  fileType?: number;
  // 主键
  id?: number;
  // 是否共享标志 0:未共享, 1:已共享
  isShare?: number;
  // 主名称
  mainName?: string;
  // 父级ID (根目录为0)
  parentId?: number;
  // 父节点路径|分隔
  parentPath?: string;
  // 共享时间
  shareTime?: Date;
  // 租户主键
  tenantId?: string;
  // 上传时间
  uploadTime?: Date;
  // 所属用户ID
  userId?: number;
  [property: string]: any;
}
interface RegionInfo {
  // 城市
  city?: string;
  // 城市代码
  cityCode?: string;
  // 城市拼音
  cityPinyin?: string;
  // 城市简称
  cityShortName?: string;
  // 地区
  district?: string;
  // 地区拼音
  districtPinyin?: string;
  // 地区简称
  districtShortName?: string;
  // 拼音首字母
  firstChar?: string;
  // 主键
  id?: string;
  // 拼音简写大写
  jianpin?: string;
  // 纬度
  lat?: string;
  // 级别类型
  levelType?: string;
  // 经度
  lng?: string;
  // 名称
  name?: string;
  // 父节点id
  parentId?: string;
  // 父节点路径
  parentPath?: string;
  // 拼音
  pinyin?: string;
  // 省份
  province?: string;
  // 省份拼音
  provincePinyin?: string;
  // 省份简称
  provinceShortName?: string;
  // 备注1
  remark1?: string;
  // 备注2
  remark2?: string;
  // 短名称
  shortName?: string;
  // 邮政编码
  zipCode?: string;
  [property: string]: any;
}
export type { BaseDataParams, ConfigInfo, DriveFileInfo, PageListParams, Pagination, RegionInfo };
