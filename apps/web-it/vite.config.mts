import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/jxct/api/bpm': {
            changeOrigin: true,
            configure: (proxy) => {
              proxy.on('proxyReq', (proxyReq) => {
                proxyReq.setHeader('Authorization', 'Bearer 4a972e5230114f8a80b785286dbe72a3');
              });
            },
            rewrite: (path) => path.replace(/^\/jxct\/api/, ''),
            // mock代理目标地址
            target: 'http://*************:48080/admin-api',
            ws: true,
          },
          '/jxct/api': {
            changeOrigin: true,
            // mock代理目标地址
            target: 'http://*************',
            ws: true,
          },
          // '/jxct/api': {
          //   changeOrigin: true,
          //   rewrite: (path) => path.replace(/^\/jxct\/api/, ''),
          //   // mock代理目标地址
          //   target: 'http://localhost:5320/api',
          //   ws: true,
          // },
        },
      },
    },
  };
});
