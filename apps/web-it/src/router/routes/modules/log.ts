import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'streamline:log',
      title: $t('page.log.title'),
    },
    name: 'Log',
    path: '/log',
    children: [
      {
        name: 'LogSystem',
        path: 'system',
        component: () => import('#/views/log/system/index.vue'),
        meta: {
          icon: 'icon-park-outline:log',
          title: $t('page.log.system'),
        },
      },
      {
        name: 'LogSystemDetail',
        path: 'system/detail',
        component: () => import('#/views/log/system/detail.vue'),
        meta: {
          title: $t('page.log.systemDetail'),
          activePath: '/log/system',
          hideInMenu: true,
        },
      },
      {
        name: 'LogLogin',
        path: 'login',
        component: () => import('#/views/log/login/index.vue'),
        meta: {
          icon: 'icon-park-outline:log',
          title: $t('page.log.login'),
        },
      },
      {
        name: 'LogLoginDetail',
        path: 'login/detail',
        component: () => import('#/views/log/login/detail.vue'),
        meta: {
          title: $t('page.log.loginDetail'),
          activePath: '/log/login',
          hideInMenu: true,
        },
      },
      {
        name: 'LogThirdParty',
        path: 'third-party',
        component: () => import('#/views/log/third-party/index.vue'),
        meta: {
          icon: 'icon-park-outline:log',
          title: $t('page.log.third-party'),
        },
      },
    ],
  },
];
export default routes;
