import type { PageListParams, Pagination } from '@vben/types';

import { requestClient } from '#/api/request';

export interface LogSystemInfo {
  pid: string;
  type: string;
  title: string;
  platform: string;
  time: number;
  serviceId: string;
  serverIp: string;
  serverHost: string;
  status: string;
  env: string;
  remoteIp: string;
  remoteAddress: string;
  userAgent: string;
  client: string;
  os: string;
  device: string;
  path: string;
  requestUrl: string;
  method: string;
  response: string;
  request: string;
  userId: string;
  userName: string;
  account: string;
  operateTime: number;
  tenantId: string;
}

export interface ThirdPartyLogInfo {
  // 调用次数
  callCount?: number;
  // 调用时间
  callTime?: Date;
  // 错误信息
  errorMsg?: string;
  // 主键
  id?: number;
  // 请求参数（JSON格式）
  requestParams?: string;
  // 响应数据（JSON格式）
  responseData?: string;
  // 三方服务名称
  serviceName?: string;
  // 调用状态：0-失败，1-成功
  status?: number;
  // 租户主键
  tenantId?: string;
  // 调用人ID
  userId?: number;
  // 调用人姓名
  userName?: string;
  [property: string]: any;
}

export async function getLogPageListApi(params: PageListParams) {
  return requestClient.get('/log/system/page-list', { params });
}
export async function getLogDetailApi(params: { pid: string }) {
  return requestClient.get('/log/system/detail', { params });
}
export async function getThirdPartyLogPageListApi(params: PageListParams) {
  return requestClient.get<Pagination<ThirdPartyLogInfo>>('/infra/thirdparty/log/page', { params });
}
export async function getThirdPartyListApi() {
  return requestClient.get('/infra/thirdparty/log/serviceNames');
}
