<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import type { AppInfo } from '@vben/types';

import type { LogSystemInfo } from '#/api';

import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Button } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getLogPageListApi } from '#/api';

const router = useRouter();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'userName',
      label: '用户',
    },
    {
      component: 'Input',
      fieldName: 'title',
      label: '类型',
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: '登录时间',
      componentProps: {
        showTime: true,
      },
    },
  ],
  fieldMappingTime: [['createTime', ['createTimeStart', 'createTimeEnd'], 'x']],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions<AppInfo> = {
  columns: [
    { field: 'type', title: '类型' },
    { field: 'account', title: '账号' },
    { field: 'userName', title: '用户' },
    { field: 'platform', title: '平台' },
    { field: 'loginTime', title: '登录时间', formatter: 'formatDate' },
    { field: 'method', title: '方式' },
    { field: 'remoteIp', title: '登录IP' },
    { field: 'remoteAddress', title: '登录地址' },
    { field: 'os', title: '系统' },
    { field: 'client', title: '客户端' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getLogPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const viewDetail = (row: LogSystemInfo) => {
  router.push({ name: 'LogLoginDetail', query: { pid: row.pid } });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <Button type="link" @click="viewDetail(row)">
          {{ $t('base.detail') }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
