<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { computed, defineAsyncComponent, provide, reactive, ref, unref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseAttachmentList, BaseFilePickList } from '#/adapter/base-ui';
import {
  addBuildingInspectionApi,
  addIndustryInspectionApi,
  addWarehouseInspectionApi,
  editInspectionApi,
  getInspectionDetailApi,
  submitInspectionApi,
} from '#/api';
import BaseForm from '#/views/operation/inspection/manage/components/base-form.vue';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const emit = defineEmits(['ok', 'register']);
const PurchaseSalesForm = defineAsyncComponent(
  () => import('#/views/operation/inspection/manage/components/purchase-sales-form.vue'),
);
const ContractForm = defineAsyncComponent(
  () => import('#/views/operation/inspection/manage/components/contract-form.vue'),
);
const PartnerCompanyForm = defineAsyncComponent(
  () => import('#/views/operation/inspection/manage/components/partner-company-form.vue'),
);
const CoreCompanyForm = defineAsyncComponent(
  () => import('#/views/operation/inspection/manage/components/core-company-form.vue'),
);
const CompanyListForm = defineAsyncComponent(
  () => import('#/views/operation/inspection/manage/components/company-list-form.vue'),
);

const colSpan = COL_SPAN_PROP;
const state = reactive<{ id?: number; loading: { submit: boolean } }>({
  id: undefined,
  loading: {
    submit: false,
  },
});
const pageTitle = computed(() => {
  return state.id ? '编辑' : '新增';
});
const sleep = (ms: number = 200) => new Promise((resolve) => setTimeout(resolve, ms));
const renderState = reactive({
  showPurchaseSales: false,
  showContract: false,
  showPartnerCompany: false,
  showCoreCompany: false,
  showSupplierCompany: false,
  showDownCompany: false,
  showWarehouseCompany: false,
});
const BaseFormRef = ref();
const PurchaseSalesFormRef = ref();
const ContractFormRef = ref();
const PartnerCompanyListFormRef = ref();
const CoreCompanyFormRef = ref();
const SupplierCompanyListFormRef = ref();
const DownCompanyListFormRef = ref();
const WarehouseCompanyListFormRef = ref();
const coreCompanyTitle = computed(() => {
  return inspectionForm.value.projectType === 'INDUSTRY' ? '核心企业投后管理情况' : '担保企业投后管理情况';
});
const inspectionForm = ref<Partial<InspectionInfo>>({
  inspectionDate: Number(dayjs().startOf('day').format('x')),
  reportDate: Number(dayjs().startOf('day').format('x')),
});
async function restartRenderState(projectType?: string) {
  if (!projectType) {
    Object.keys(renderState).forEach((key) => {
      (renderState as any)[key] = false;
    });
  } else if (['BUILDING', 'INDUSTRY'].includes(projectType)) {
    renderState.showPurchaseSales = false;
    renderState.showContract = false;
    renderState.showPartnerCompany = false;
    renderState.showCoreCompany = false;

    renderState.showPurchaseSales = true;
    await sleep();
    renderState.showContract = true;
    await sleep();
    renderState.showPartnerCompany = true;
    await sleep();
    renderState.showCoreCompany = true;
    await sleep();
  } else if (['WAREHOUSE'].includes(projectType)) {
    renderState.showSupplierCompany = false;
    renderState.showDownCompany = false;
    renderState.showWarehouseCompany = false;
    renderState.showSupplierCompany = true;
    await sleep();
    renderState.showDownCompany = true;
    await sleep();
    renderState.showWarehouseCompany = true;
    await sleep();
  }
}
const changeProject = async () => {
  await restartRenderState(inspectionForm.value.projectType);
};
const getDetail = async () => {
  if (state.id) {
    inspectionForm.value = await getInspectionDetailApi(state.id);
    await restartRenderState(inspectionForm.value.projectType);
    if (inspectionForm.value.projectType && ['BUILDING', 'INDUSTRY'].includes(inspectionForm.value.projectType)) {
      ContractFormRef.value.init();
      PartnerCompanyListFormRef.value.init();
      CoreCompanyFormRef.value.init();
    } else if (inspectionForm.value.projectType && ['WAREHOUSE'].includes(inspectionForm.value.projectType)) {
      SupplierCompanyListFormRef.value.init(inspectionForm.value.supplierDataDetails);
      DownCompanyListFormRef.value.init(inspectionForm.value.customerDataDetails);
      WarehouseCompanyListFormRef.value.init(inspectionForm.value.warehouseDataDetails);
    }
  }
};
const init = (data: { id: number }) => {
  state.id = data.id;
  getDetail();
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const save = async (type: string) => {
  const formData = unref(inspectionForm);
  await BaseFormRef.value.save();
  if (formData.projectType && ['BUILDING', 'INDUSTRY'].includes(formData.projectType)) {
    const { purchaseSalesData } = await PurchaseSalesFormRef.value.save();
    await ContractFormRef.value.save();
    const { partnerFinancialsData } = await PartnerCompanyListFormRef.value.save();
    const { energyConsumptionData, cgFinancialsData } = await CoreCompanyFormRef.value.save();
    formData.purchaseSalesData = purchaseSalesData;
    formData.partnerFinancialsData = partnerFinancialsData;
    formData.energyConsumptionData = energyConsumptionData;
    formData.cgFinancialsData = cgFinancialsData;
  } else if (formData.projectType && ['WAREHOUSE'].includes(formData.projectType)) {
    const { companyList: supplierDataDetails } = await SupplierCompanyListFormRef.value.save();
    const { companyList: customerDataDetails } = await DownCompanyListFormRef.value.save();
    const { companyList: warehouseDataDetails } = await WarehouseCompanyListFormRef.value.save();
    formData.supplierDataDetails = supplierDataDetails;
    formData.customerDataDetails = customerDataDetails;
    formData.warehouseDataDetails = warehouseDataDetails;
  }
  try {
    let api = null;
    switch (formData.projectType) {
      case 'BUILDING': {
        api = addBuildingInspectionApi;
        break;
      }
      case 'INDUSTRY': {
        api = addIndustryInspectionApi;
        break;
      }
      case 'WAREHOUSE': {
        api = addWarehouseInspectionApi;
        break;
      }
    }
    if (formData.id) {
      api = editInspectionApi;
    }
    if (type === 'submit') {
      api = submitInspectionApi;
    }
    if (!api) {
      throw new Error('不支持的项目类型');
    }
    changeOkLoading(true);
    state.loading.submit = true;
    await api(formData);
    message.success('保存成功');
    emit('ok');
    closePopup();
  } finally {
    state.loading.submit = false;
    changeOkLoading(false);
  }
};

const reportDate = computed({
  get() {
    return inspectionForm.value.reportDate ? dayjs(inspectionForm.value.reportDate).format('x') : undefined;
  },
  set(newValue: string) {
    inspectionForm.value.reportDate = newValue ? Number(newValue) : undefined;
  },
});
provide(inspectionFormKey, inspectionForm);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="pageTitle" @register="registerPopup" @ok="save">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="state.loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基本情况 -->
      <BaseForm ref="BaseFormRef" @change-project="changeProject" />
      <div v-if="inspectionForm.projectType && ['INDUSTRY', 'BUILDING'].includes(inspectionForm.projectType)">
        <!-- 采购销售情况 -->
        <PurchaseSalesForm ref="PurchaseSalesFormRef" v-if="renderState.showPurchaseSales" />
        <!-- 合同履约情况 -->
        <ContractForm ref="ContractFormRef" v-if="renderState.showContract" />
        <!-- 合作企业投后管理情况 -->
        <PartnerCompanyForm ref="PartnerCompanyListFormRef" v-if="renderState.showPartnerCompany" />
        <!-- 核心企业/担保企业投后管理情况 -->
        <CoreCompanyForm ref="CoreCompanyFormRef" :title="coreCompanyTitle" v-if="renderState.showCoreCompany" />
        <a-form v-bind="FORM_PROP">
          <BasicCaption content="抵质押物检查情况" class="mb-4" />
          <a-form-item label="存续情况" name="collateralStatus" v-bind="FULL_FORM_ITEM_PROP">
            <a-textarea v-model:value="inspectionForm.collateralStatus" :rows="4" />
          </a-form-item>
        </a-form>
      </div>
      <div v-if="inspectionForm.projectType && ['WAREHOUSE'].includes(inspectionForm.projectType)">
        <!-- 供应商情况 -->
        <CompanyListForm ref="SupplierCompanyListFormRef" company-type="supplier" />
        <!-- 下游客户情况 -->
        <CompanyListForm ref="DownCompanyListFormRef" company-type="down" />
        <!-- 仓库情况 -->
        <CompanyListForm ref="WarehouseCompanyListFormRef" company-type="warehouse" />
      </div>
      <a-form v-bind="FORM_PROP">
        <BasicCaption content="检查结果" class="mb-4" />
        <a-form-item label="其他情况" name="otherInfoSummary" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.otherInfoSummary" :rows="4" />
        </a-form-item>
        <a-form-item label="检查结论" name="conclusion" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.conclusion" :rows="4" />
        </a-form-item>
        <a-form-item label="资产分类" name="assetClassification" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.assetClassification" :rows="4" />
        </a-form-item>
        <BasicCaption content="其他信息" class="mb-4" />
        <a-row>
          <a-col v-bind="colSpan">
            <a-form-item label="运营人员" name="operationsOfficer">
              <a-input v-model:value="inspectionForm.operationsOfficer" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="复核人员" name="reviewOfficer">
              <a-input v-model:value="inspectionForm.reviewOfficer" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="报告日期" name="reportDate">
              <a-date-picker v-model:value="reportDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="底稿附件" name="draftAttachment" v-bind="FULL_FORM_ITEM_PROP">
              <a-textarea v-model:value="inspectionForm.draftAttachment" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <BasicCaption content="现场图片" class="mb-4" />
      <div>
        <BaseFilePickList v-model:id-list="inspectionForm.imageIdList" />
      </div>
      <BaseAttachmentList
        v-model="inspectionForm.attachmentList"
        :business-id="inspectionForm.id"
        business-type="COMPANY"
        edit-mode
      />
    </div>
  </BasicPopup>
</template>

<style></style>
