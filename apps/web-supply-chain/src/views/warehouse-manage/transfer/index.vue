<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { TransferApplyInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { changeTransferApi, delTransferApi, getTransferListApi } from '#/api';

import Detail from './detail.vue';
import Form from './form.vue';

const dictStore = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'transferApplyCode',
      label: '提货申请编号',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Input',
      fieldName: 'purchaseOrderCode',
      label: '关联销售订单编号',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Input',
      fieldName: 'customerCompanyName',
      label: '仓储企业',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('APPROVAL_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'customerCompanyName',
      label: '客户企业',
    },
    {
      component: 'Input',
      fieldName: 'executorCompanyName',
      label: '控货方',
    },
    {
      component: 'RangePicker',
      fieldName: 'applyDate',
      label: '申请提货日期',
    },
  ],
  fieldMappingTime: [['applyDate', ['applyStartDate', 'applyEndDate'], 'YYYY-MM-DD 00:00:00']],
  showCollapseButton: true,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'transferApplyCode', title: '提货申请编号' },
    { field: 'warehouseName', title: '仓库名称' },
    {
      field: 'warehouseCompanyName',
      title: '仓储企业',
    },
    {
      field: 'customerCompanyName',
      title: '客户企业',
    },
    {
      field: 'executorCompanyName',
      title: '控货方',
    },
    { field: 'applyDate', title: '申请提货日期' },
    {
      field: 'businessStructure',
      title: '货款含税金额',
      formatter: () => {
        return 'Not';
      },
    },
    { field: 'approvalStatus', title: '审批状态', formatter: ['formatStatus', 'APPROVAL_STATUS'] },
    { field: 'status', title: '业务状态', formatter: ['formatStatus', 'BUS_STATUS'] },
    {
      field: 'inoutType',
      title: '出库状态',
      formatter: ({ cellValue }) => {
        return cellValue ? '已完出库' : '未出库';
      },
    },
    { field: 'businessDate', title: '关联销售订单名称' },
    {
      field: 'salesOrderCode',
      title: '关联销售订单编号',
    },
    {
      field: 'projectName',
      title: '所属项目名称',
    },
    {
      field: 'projectCode',
      title: '所属项目编号',
    },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    { field: 'createDept', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getTransferListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: TransferApplyInfo) => {
  openFormPopup(true, row);
};
const detail = (row: TransferApplyInfo) => {
  openDetailPopup(true, row);
};
const invalidate = (row: TransferApplyInfo) => {
  AntdModal.confirm({
    title: '确认作废',
    content: `此操作将完成出库该数据，是否继续？`,
    async onOk() {
      await changeTransferApi({ id: row.id, orderState: '1' });
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const del = async (row: TransferApplyInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delTransferApi(row.id || '');
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="invalidate(row)"> 完成出库 </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
