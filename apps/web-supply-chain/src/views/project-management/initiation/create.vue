<script setup lang="ts">
import { computed, watch, ref, reactive } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';

import {
  type ProjectBaseInfo,
  type ProjectPartners,
  projectProposalAddApi,
  projectProposalEditApi,
  projectProposalDetailApi,
  projectProposalSubmitApi,
  getCompanyApi,
  getUserListApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
} from '#/api';

import type { GridApi, VxeGridProps } from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { BasicPopup, usePopupInner, FeUserSelect } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';
import { BaseRegionPicker, BaseFilePickList, BaseAttachmentList } from '#/adapter/base-ui';

import {
  Button,
  Col,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Textarea,
  DatePicker,
  Modal,
} from 'ant-design-vue';

const emit = defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();

// 根据接口定义初始化产品信息
const defaultForm = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  projectCode: new Date().getTime().toString(),
  projectName: undefined,
  executorCompanyCode: undefined,
  executorCompanyName: '江西财投集团有限责任公司',
  businessStructure: undefined,
  projectModel: undefined,
  purchaseMode: [],
  isGoodsControlMode: undefined,
  paymentTermDays: undefined,
  planStartDate: undefined,
  creditDueDate: undefined,
  expectedProjectScale: undefined,
  serviceFeeRate: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  remarks: undefined,
  isDeposit: undefined,
  mortgageInfoDesc: undefined,
  pledgeInfoDesc: undefined,
  mortgageInfoAttachment: undefined,
  pledgeInfoAttachment: undefined,
  guaranteeInfoDesc: undefined,
  riskControlDesc: undefined,
  creditEnhancementDesc: undefined,
  status: undefined,
  approvalStatus: undefined,
  paymentMethod: [],
  collectionMethod: [],
  settlementMethod: undefined,
  isKeyIndustry: undefined,
  isRealEnterprise: undefined,
  businessManagerId: [],
  operationManagerId: [],
  financeManagerId: [],
  riskManagerId: [],
  guaranteeCompanyCode: undefined,
  guaranteeCompanyName: undefined,
  creditType: undefined,
  projectReviewId: undefined,
  partyBranchId: undefined,
  generalManagerId: undefined,
  reviewNodeId: undefined,
  attachmentList: [],
  reviewRecordList: [],
  branchCommitteeList: [],
  branchRecordList: [],
  generalManagerList: [],
  managerRecordList: [],
  projectPartners: [
    {
      id: undefined,
      version: undefined,
      projectId: undefined,
      partnerType: undefined,
      companyCode: undefined,
      companyName: undefined,
      subLimitAmount: undefined,
      occupyLimit: undefined,
      creditType: undefined,
      expiryDate: undefined,
    },
  ],
};

let detailForm = reactive<Partial<ProjectBaseInfo>>(cloneDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
const pageLoading = ref(false);
const loading = reactive({
  submit: false,
});
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  businessStructure: [{ required: true, message: '请选择业务结构', trigger: 'change' }],
  projectModel: [{ required: true, message: '请选择项目模式', trigger: 'change' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业', trigger: 'change' }],
  businessManagerId: [{ required: true, message: '请选择业务负责人', trigger: 'change', type: 'array' }],
  settlementMethod: [{ required: true, message: '请选择结算方式', trigger: 'change' }],
  isDeposit: [{ required: true, message: '请选择是否有保证金', trigger: 'change' }],
  purchaseMode: [{ required: true, message: '请选择采购模式', trigger: 'change', type: 'array' }],
  isGoodsControlMode: [{ required: true, message: '请选择是否是控货模式', trigger: 'change' }],
  serviceFeeRate: [{ required: true, message: '请输入合作费率', trigger: 'change' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'change' }],
  expectedProjectScale: [{ required: true, message: '请输入项目总额度', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change', type: 'array' }],
  collectionMethod: [{ required: true, message: '请选择回款方式', trigger: 'change', type: 'array' }],
  isKeyIndustry: [{ required: true, message: '请选择是否支持重点产业链', trigger: 'change' }],
  isRealEnterprise: [{ required: true, message: '请选择是否支持实体企业', trigger: 'change' }],
};

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

// 处理逗号分隔字符串的函数
const processCommaSeparatedField = (fieldValue: any): string | undefined => {
  if (Array.isArray(fieldValue)) {
    return fieldValue.join(',');
  } else if (typeof fieldValue === 'string') {
    return fieldValue;
  }
  return undefined;
};

// 解析逗号分隔字符串为数组的函数
const parseCommaSeparatedField = (fieldValue: any): any[] => {
  if (typeof fieldValue === 'string' && fieldValue) {
    return fieldValue.split(',').filter((item) => item !== '');
  } else if (Array.isArray(fieldValue)) {
    return fieldValue;
  }
  return [];
};

const formRef = ref();

const init = async (data: ProjectBaseInfo) => {
  pageLoading.value = true;
  await getCompanyList();
  if (data.id) {
    const res: Partial<ProjectBaseInfo> = await projectProposalDetailApi(data.id);
    Object.assign(detailForm, res);

    detailForm.purchaseMode = parseCommaSeparatedField(detailForm.purchaseMode);
    detailForm.paymentMethod = parseCommaSeparatedField(detailForm.paymentMethod);
    detailForm.collectionMethod = parseCommaSeparatedField(detailForm.collectionMethod);

    // 强制校验并转换projectPartners字段
    if (!Array.isArray(res.projectPartners) || res.projectPartners === null) {
      detailForm.projectPartners = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.projectPartners = [...res.projectPartners];
    }

    // 强制刷新表格
    if (gridApiSupplier?.grid) {
      await gridApiSupplier.grid.reloadData(detailForm.projectPartners.filter((item) => item.partnerType === '1'));
    }
    if (gridApiPurchaser?.grid) {
      await gridApiPurchaser.grid.reloadData(detailForm.projectPartners.filter((item) => item.partnerType === '2'));
    }
    if (gridApiCredit?.grid) {
      await gridApiCredit.grid.reloadData(detailForm.projectPartners.filter((item) => item.partnerType === '3'));
    }
  }
  pageLoading.value = false;
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};
const labelCol = { style: { width: '150px' } };

const accountList = ref<ProjectPartners[]>([]);

const save = async (type: string) => {
  await formRef.value.validate();
  changeOkLoading(true);

  let api = detailForm.id ? projectProposalEditApi : projectProposalAddApi;
  if (type === 'submit') {
    api = projectProposalSubmitApi;
  }

  const submitData = { ...detailForm };
  submitData.purchaseMode = processCommaSeparatedField(detailForm.purchaseMode);
  submitData.paymentMethod = processCommaSeparatedField(detailForm.paymentMethod);
  submitData.collectionMethod = processCommaSeparatedField(detailForm.collectionMethod);

  try {
    // 获取各表格数据并打标签
    const supplierData = gridApiSupplier.grid.getTableData().tableData.map((row) => ({
      ...row,
      partnerType: '1', // 标记为上游企业
    }));
    const purchaserData = gridApiPurchaser.grid.getTableData().tableData.map((row) => ({
      ...row,
      partnerType: '2', // 标记为下游企业
    }));
    const creditData = gridApiCredit.grid.getTableData().tableData.map((row) => ({
      ...row,
      partnerType: '3', // 标记为终端企业
    }));

    submitData.projectPartners = [...supplierData, ...purchaserData, ...creditData];

    // 调用接口提交
    const res = await api(submitData as ProjectBaseInfo);

    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

// 新增初始化方法
const setAccountData = (data: ProjectPartners[]) => {
  accountList.value = data;
  // 更新所有相关表格
  if (gridApiSupplier.grid) {
    gridApiSupplier.grid.reloadData(accountList.value);
  }
  if (gridApiPurchaser.grid) {
    gridApiPurchaser.grid.reloadData(accountList.value);
  }
  if (gridApiCredit.grid) {
    gridApiCredit.grid.reloadData(accountList.value);
  }
};

const gridOptions: VxeGridProps = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridOptionsSupplier: VxeGridProps = {
  ...gridOptions,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'companyName',
      title: '企业名称',
      slots: { edit: 'edit_company_name' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { edit: 'edit_company_code' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsPurchaser: VxeGridProps = {
  ...gridOptions,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'companyName',
      title: '企业名称',
      slots: { edit: 'edit_company_name' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { edit: 'edit_company_code' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsCredit: VxeGridProps = {
  ...gridOptions,
  editRules: {
    subLimitAmount: [{ required: true, message: '请输入企业额度上限' }],
    occupyLimit: [{ required: true, message: '请选择是否占用企业总额度' }],
    expiryDate: [{ required: true, message: '请选择额度到期日' }],
    creditType: [{ required: true, message: '请选择授信类型' }],
  },
  columns: [
    {
      type: 'checkbox',
      width: '60px',
      fixed: 'left',
    },
    {
      field: 'companyName',
      title: '企业名称',
      slots: { edit: 'edit_company_name' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { edit: 'edit_company_code' },
      minWidth: '160px',
    },
    {
      field: 'subLimitAmount',
      title: '企业额度上限（元）',
      editRender: {},
      slots: { edit: 'edit_sub_limit_amount' },
      minWidth: '160px',
    },
    {
      field: 'occupyLimit',
      title: '是否占用企业总额度',
      editRender: {},
      slots: { edit: 'edit_occupy_limit' },
      formatter: ['formatStatus', 'baseBooleanType'],
      minWidth: '160px',
    },
    {
      field: 'expiryDate',
      title: '额度到期日',
      editRender: {},
      slots: { edit: 'edit_expiry_date' },
      minWidth: '160px',
    },
    {
      field: 'creditType',
      title: '授信类型',
      editRender: {},
      slots: { edit: 'edit_credit_type' },
      formatter: ['formatStatus', 'CREDIT_TYPE'],
      minWidth: '160px',
    },
  ],
  data: [],
};

interface CompanySelectorModal {
  visible: boolean;
  gridApi: GridApi | null;
  company: {
    companyName: string | undefined;
    companyCode: string | undefined;
    createBy: number;
    createTime: string | undefined;
    updateBy: number;
    updateTime: string | undefined;
    version: number;
    projectId: number;
  };
}

const companySelectorModal = reactive<CompanySelectorModal>({
  visible: false,
  gridApi: null as GridApi | null,
  company: {
    companyName: undefined,
    companyCode: undefined,
    createBy: 0,
    createTime: undefined,
    updateBy: 0,
    updateTime: undefined,
    version: 0,
    projectId: detailForm.id || 0,
  },
});

// 处理企业选择变化
const handleCompanyChange = (value: string, option: any) => {
  if (value && option) {
    // 同时更新企业编码和企业名称
    companySelectorModal.company.companyCode = value;
    companySelectorModal.company.companyName = option.companyName;
  } else {
    // 如果没有选中任何企业，清空相关字段
    companySelectorModal.company.companyCode = undefined;
    companySelectorModal.company.companyName = undefined;
  }
};

const handleCompanySelectOk = () => {
  // 如果用户选择了企业，则添加到表格中
  if (companySelectorModal.company && companySelectorModal.gridApi) {
    try {
      companySelectorModal.gridApi.grid.insertAt(companySelectorModal.company, -1);
    } catch (error) {
      message.error('添加企业失败: ' + (error as Error).message);
    }
  }
  companySelectorModal.visible = false;
  companySelectorModal.company.companyCode = undefined;
};

const handleCompanySelectCancel = () => {
  companySelectorModal.visible = false;
  companySelectorModal.company.companyCode = undefined;
};

// 选择企业
const selectCompany = async (gridApi: GridApi) => {
  if (gridApi) {
    companySelectorModal.visible = true;
    companySelectorModal.gridApi = gridApi;
  }
};

// 删除行
const removeAccount = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteProjectPartners } = detailForm;

      // 确保 deleteProjectPartners 是数组
      if (!Array.isArray(deleteProjectPartners)) {
        detailForm.deleteProjectPartners = [...selectRecords];
      } else {
        detailForm.deleteProjectPartners = [...deleteProjectPartners, ...selectRecords];
      }

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

const [GridSupplier, gridApiSupplier] = useVbenVxeGrid({
  gridOptions: gridOptionsSupplier,
  tableTitle: '上游企业',
});
const [GridPurchaser, gridApiPurchaser] = useVbenVxeGrid({
  gridOptions: gridOptionsPurchaser,
  tableTitle: '下游企业',
});
const [GridCredit, gridApiCredit] = useVbenVxeGrid({
  gridOptions: gridOptionsCredit,
  tableTitle: '终端企业',
});

const filteredProjectModeOptions = ref<any[]>([]);

// 只监听真正需要的依赖
watch(
  [() => detailForm.id, () => detailForm.projectModel, () => detailForm.businessStructure],
  ([id, projectModel, businessStructure]) => {
    const options = getDictList('PROJECT_MODE');

    // 编辑模式且已有项目模式值时，返回所有选项
    if (id && projectModel) {
      filteredProjectModeOptions.value = options;
      return;
    }

    // 根据业务结构过滤选项
    filteredProjectModeOptions.value = options.map((option) => {
      if (businessStructure === 'PURCHASE') {
        if (option.value === 'BUILDING' || option.value === 'WAREHOUSE_SUPPLY') {
          return { ...option, disabled: true };
        }
      }
      return { ...option, disabled: false };
    });
  },
  { immediate: true }, // 立即执行一次
);

// 暴露方法给父组件
defineExpose({
  getAccountData() {
    // 合并所有表格的数据
    let allData: any[] = [];

    if (gridApiSupplier.grid) {
      const { tableData } = gridApiSupplier.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    if (gridApiPurchaser.grid) {
      const { tableData } = gridApiPurchaser.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    if (gridApiCredit.grid) {
      const { tableData } = gridApiCredit.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    return allData;
  },
  setAccountData,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :loading="pageLoading" :title="title" @register="registerPopup" @close="close">
    <template #insertToolbar>
      <a-space v-if="!detailForm.id || detailForm.status === 'SUBMIT'">
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption v-if="!detailForm.id" content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="项目编号" name="projectCode">
            <span v-if="detailForm.id"> {{ detailForm.projectCode }} </span>
            <!-- <span v-else> —— </span>-->
            <span v-else> {{ new Date().getTime() }} </span>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目名称" name="projectName">
            <Input
              v-model:value="detailForm.projectName"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务结构" name="businessStructure">
            <Select
              v-model:value="detailForm.businessStructure"
              :options="getDictList('BUS_STRUCTURE')"
              formatter="['formatstatus','BUS_STRUCTURE']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目模式" name="projectModel">
            <Select
              v-model:value="detailForm.projectModel"
              :options="detailForm.id ? getDictList('PROJECT_MODE') : filteredProjectModeOptions"
              formatter="['formatstatus','PROJECT_MODE']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="贸易执行企业" name="executorCompanyName">
            <Input v-model:value="detailForm.executorCompanyName" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务负责人" name="businessManagerId">
            <FeUserSelect
              v-model:value="detailForm.businessManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营负责人" name="operationManagerId">
            <FeUserSelect
              v-model:value="detailForm.operationManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="财务负责人" name="financeManagerId">
            <FeUserSelect
              v-model:value="detailForm.financeManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="风控负责人" name="riskManagerId">
            <FeUserSelect
              v-model:value="detailForm.riskManagerId"
              multiple
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否有保证金" name="isDeposit">
            <Select
              v-model:value="detailForm.isDeposit"
              :options="getDictList('baseBooleanType')"
              formatter="['formatstatus','baseBooleanType']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购模式" name="purchaseMode">
            <Select
              v-model:value="detailForm.purchaseMode"
              mode="multiple"
              :options="getDictList('PURCHASE_MODE')"
              formatter="['formatstatus','PURCHASE_MODE']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否是控货模式" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.isGoodsControlMode"
              :options="getDictList('baseBooleanType')"
              formatter="['formatstatus','baseBooleanType']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="合作费率（年%）" name="serviceFeeRate">
            <InputNumber
              v-model:value="detailForm.serviceFeeRate"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="账期（天)" name="paymentTermDays">
            <InputNumber
              v-model:value="detailForm.paymentTermDays"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目总额度(元)" name="expectedProjectScale">
            <InputNumber
              v-model:value="detailForm.expectedProjectScale"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预计开始日期" name="planStartDate">
            <DatePicker
              v-model:value="detailForm.planStartDate"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="授信到期日" name="creditDueDate">
            <DatePicker
              v-model:value="detailForm.creditDueDate"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <Row :gutter="12">
            <Col :span="12">
              <FormItem label="项目地点" name="detailAddress">
                <BaseRegionPicker
                  v-model:province="detailForm.province"
                  v-model:city="detailForm.city"
                  v-model:district="detailForm.district"
                  :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
                />
              </FormItem>
            </Col>
            <Col :span="12">
              <FormItem name="detailAddress">
                <Input
                  v-model:value="detailForm.detailAddress"
                  :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
                />
              </FormItem>
            </Col>
          </Row>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="担保企业" name="guaranteeCompanyCode">
            <Select
              v-model:value="detailForm.guaranteeCompanyCode"
              :options="companyOptions"
              :field-names="{ label: 'companyName', value: 'companyCode' }"
              show-search
              :filter-option="
                (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
              "
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="付款方式" name="paymentMethod">
            <Select
              v-model:value="detailForm.paymentMethod"
              mode="multiple"
              :options="getDictList('PAYMENT_WAY')"
              formatter="['formatstatus','PAYMENT_WAY']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="回款方式" name="collectionMethod">
            <Select
              v-model:value="detailForm.collectionMethod"
              mode="multiple"
              :options="getDictList('PAYMENT_WAY')"
              formatter="['formatstatus','PAYMENT_WAY']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="结算方式" name="settlementMethod">
            <Select
              v-model:value="detailForm.settlementMethod"
              :options="getDictList('SETTLEMENT_MODE')"
              formatter="['formatstatus', 'SETTLEMENT_MODE']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="授信类型" name="creditType">
            <Select
              v-model:value="detailForm.creditType"
              :options="getDictList('CREDIT_TYPE')"
              formatter="['formatstatus', 'CREDIT_TYPE']"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持重点产业链" name="isKeyIndustry">
            <Select
              v-model:value="detailForm.isKeyIndustry"
              :options="getDictList('baseBooleanType')"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持实体企业" name="isRealEnterprise">
            <Select
              v-model:value="detailForm.isRealEnterprise"
              :options="getDictList('baseBooleanType')"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea
              v-model:value="detailForm.remarks"
              :rows="3"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </FormItem>
        </Col>
      </Row>

      <!-- 关联企业及敞口信息 -->
      <BasicCaption content="关联企业及敞口信息" />
      <div>
        <GridSupplier>
          <template #toolbarTools v-if="!detailForm.id || detailForm.status === 'SUBMIT'">
            <Button class="mr-2" type="primary" @click="() => selectCompany(gridApiSupplier)">选择企业</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiSupplier)">删行</Button>
          </template>
          <template #edit_company_name="{ row }">
            {{ row.companyName }}
          </template>
          <template #edit_company_code="{ row }">
            {{ row.companyCode }}
          </template>
        </GridSupplier>
      </div>
      <div>
        <GridPurchaser>
          <template #toolbarTools v-if="!detailForm.id || detailForm.status === 'SUBMIT'">
            <Button class="mr-2" type="primary" @click="() => selectCompany(gridApiPurchaser)">选择企业</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiPurchaser)">删行</Button>
          </template>
          <template #edit_company_name="{ row }">
            {{ row.companyName }}
          </template>
          <template #edit_company_code="{ row }">
            {{ row.companyCode }}
          </template>
        </GridPurchaser>
      </div>
      <div>
        <GridCredit>
          <template #toolbarTools v-if="!detailForm.id || detailForm.status === 'SUBMIT'">
            <Button class="mr-2" type="primary" @click="() => selectCompany(gridApiCredit)">选择企业</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiCredit)">删行</Button>
          </template>
          <template #edit_company_name="{ row }">
            {{ row.companyName }}
          </template>
          <template #edit_company_code="{ row }">
            {{ row.companyCode }}
          </template>
          <template #edit_sub_limit_amount="{ row }">
            <InputNumber
              v-model:value="row.subLimitAmount"
              placeholder="请输入企业额度上限"
              class="w-full"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </template>
          <template #edit_occupy_limit="{ row }">
            <Select
              v-model:value="row.occupyLimit"
              placeholder="是否占用企业总额度"
              :options="getDictList('baseBooleanType')"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </template>
          <template #edit_expiry_date="{ row }">
            <DatePicker
              v-model:value="row.expiryDate"
              placeholder="选择额度到期日"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </template>
          <template #edit_credit_type="{ row }">
            <Select
              v-model:value="row.creditType"
              placeholder="请选择授信类型"
              :options="getDictList('CREDIT_TYPE')"
              :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
            />
          </template>
        </GridCredit>
      </div>

      <!-- 增信措施 -->
      <BasicCaption content="增信措施" />
      <div class="mt-5">
        <FormItem label="增信措施描述">
          <Textarea
            v-model:value="detailForm.creditEnhancementDesc"
            :rows="3"
            :disabled="!!detailForm.id && detailForm.status !== 'SUBMIT'"
          />
        </FormItem>
      </div>

      <!-- 附件信息 -->
      <BaseAttachmentList
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_PROJECT"
        :edit-mode="!detailForm.id || detailForm.status === 'SUBMIT'"
      />
    </Form>

    <!-- 企业选择 Modal -->
    <Modal
      v-model:open="companySelectorModal.visible"
      title="选择企业"
      @ok="handleCompanySelectOk"
      @cancel="handleCompanySelectCancel"
      width="600px"
    >
      <Form style="margin-top: 20px">
        <FormItem label="选择企业">
          <Select
            v-model:value="companySelectorModal.company.companyCode"
            placeholder="请选择企业"
            :options="companyOptions"
            :field-names="{ label: 'companyName', value: 'companyCode' }"
            @change="handleCompanyChange"
            style="width: 100%"
          />
        </FormItem>
        <FormItem label="统一社会信用代码">
          <div v-if="companySelectorModal.company.companyCode">
            {{ companySelectorModal.company.companyCode }}
          </div>
          <div v-else class="text-gray-400">请选择企业</div>
        </FormItem>
      </Form>
    </Modal>
  </BasicPopup>
</template>

<style scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.ant-input-number,
.ant-picker {
  width: 100% !important;
}

.ant-picker-focused {
  width: 100% !important;
}
</style>
