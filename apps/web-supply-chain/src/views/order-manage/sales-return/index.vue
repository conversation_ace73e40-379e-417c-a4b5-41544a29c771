<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions, formatDate } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { changeSalesReturnApi, delSalesReturnApi, getSalesReturnListApi } from '#/api';

import Detail from './detail.vue';
import Form from './form.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'salesReturnOrderCode',
      label: '销售退货单编号',
    },
    {
      component: 'Input',
      fieldName: 'salesReturnOrderName',
      label: '销售退货单名称',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Input',
      fieldName: 'salesOrderCode',
      label: '关联销售订单编号',
    },
    {
      component: 'Input',
      fieldName: 'salesOrderName',
      label: '关联销售订单名称',
    },
    {
      component: 'Input',
      fieldName: 'purchaserCompanyName',
      label: '下游企业',
    },
    {
      component: 'Input',
      fieldName: 'executorCompanyName',
      label: '贸易执行企业',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('APPROVAL_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'isCompleted',
      label: '完成状态',
      componentProps: {
        options: [
          {
            label: '已完成',
            value: 1,
          },
          {
            label: '未完成',
            value: 0,
          },
        ],
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'businessDate',
      label: '业务日期',
    },
  ],
  fieldMappingTime: [['businessDate', ['businessStartDate', 'businessEndDate'], 'YYYY-MM-DD 00:00:00']],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'salesReturnOrderCode', title: '销售退货单编号' },
    { field: 'salesReturnOrderName', title: '销售退货单名称', slots: { default: 'TASKTYPE' } },
    {
      field: 'purchaserCompanyName',
      title: '下游企业',
    },
    {
      field: 'executorCompanyName',
      title: '贸易执行企业',
    },
    { field: 'projectName', title: '所属项目名称' },
    { field: 'projectCode', title: '所属项目编号' },
    { field: 'salesOrderName', title: '关联销售订单名称' },
    { field: 'salesOrderCode', title: '关联销售订单编号' },
    { field: 'businessDate', title: '业务日期', formatter: ({ cellValue }) => formatDate(cellValue) },
    { field: 'approvalStatus', title: '审批状态', formatter: ['formatStatus', 'APPROVAL_STATUS'] },
    {
      field: 'status',
      title: '业务状态',
      formatter: ['formatStatus', 'BUS_STATUS'],
    },
    {
      field: 'isCompleted',
      title: '完成状态',
      formatter: ({ cellValue }) => {
        return cellValue ? '已完成' : '未完成';
      },
    },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    { field: 'createDept', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getSalesReturnListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: OrderInfo) => {
  openFormPopup(true, row);
};
const detail = (row: OrderInfo) => {
  openDetailPopup(true, row);
};

const invalidate = (row: OrderInfo, type: string) => {
  AntdModal.confirm({
    title: `确认${type}`,
    content: `此操作将${type}该数据，是否继续？`,
    async onOk() {
      const params = {
        id: row.id,
        status: row.status,
        approvalStatus: row.approvalStatus,
        isCompleted: row.isCompleted,
      };
      switch (type) {
        case '作废': {
          params.status = 'CANCELLED';

          break;
        }
        case '取消完成': {
          params.isCompleted = 0;

          break;
        }
        case '完成': {
          params.isCompleted = 1;

          break;
        }
      }
      await changeSalesReturnApi(params);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const del = async (row: OrderInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delSalesReturnApi(row.id || '');
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <!-- 待提交才能编辑 -->
          <TypographyLink @click="edit(row)" v-if="row.status === 'DRAFTING'">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <!-- 待提交才能删除 -->
          <TypographyLink type="danger" @click="del(row)" v-if="row.status === 'DRAFTING'">
            {{ $t('base.del') }}
          </TypographyLink>
          <!-- 只有审批拒绝才能作废 -->
          <TypographyLink type="danger" @click="invalidate(row, '作废')" v-if="row.approvalStatus === 'REJECTED'">
            {{ $t('base.invalidate') }}
          </TypographyLink>
          <!-- 未完成并且已生效才能完成 -->
          <TypographyLink @click="invalidate(row, '完成')" v-if="!row.isCompleted && row.status === 'EFFECTIVE'">
            {{ $t('base.completed') }}
          </TypographyLink>
          <TypographyLink @click="invalidate(row, '取消完成')" v-if="row.isCompleted">
            {{ $t('base.cancel') }}
          </TypographyLink>
        </a-space>
      </template>
      <template #TASKTYPE="{ row }">
        {{ row.salesReturnOrderName }}
        <a-tag color="red" v-if="row.taskType !== '0'">
          {{ dictStore.formatter(String(row.taskType), 'TASK_TYPE') }}
        </a-tag>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
