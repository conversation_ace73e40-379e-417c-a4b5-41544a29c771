<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addSalesReturnListApi,
  editSalesReturnListApi,
  getOrderListApi,
  infoSalesListApi,
  infoSalesReturnListApi,
} from '#/api';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);
const dictStore = useDictStore();
const orderInfoForm = ref<OrderInfo>({});
const formRef = ref();
const colSpan = { md: 12, sm: 24 };
const rules: Record<string, Rule[]> = {
  salesReturnOrderName: [{ required: true, message: '请输入采购退货订单名称', trigger: 'blur' }],
  salesOrderCode: [{ required: true, message: '请选择关联采购订单', trigger: 'change' }],
  businessDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
};
const title = computed(() => {
  return orderInfoForm.value.id ? '编辑销售退货订单' : '新增销售退货订单';
});
const productGridRef = ref();
const init = async (data: OrderInfo) => {
  if (data.id) {
    orderInfoForm.value = await infoSalesReturnListApi({ id: data.id });
    productGridRef.value.setProductData(orderInfoForm.value.salesReturnOrderItemVOS);
  } else {
    orderInfoForm.value = data;
  }
};
// 处理关联订单选择事件的函数
const handleCodesSelect = async (_value: string, option: any) => {
  orderInfoForm.value.salesOrderId = option.value;
  orderInfoForm.value.salesOrderName = option.label;
  orderInfoForm.value.projectName = option.projectName;
  orderInfoForm.value.projectCode = option.projectCode;

  orderInfoForm.value.purchaserCompanyName = option.purchaserCompanyName; // 下游企业
  orderInfoForm.value.purchaserCompanyCode = option.purchaserCompanyCode;
  orderInfoForm.value.executorCompanyName = option.executorCompanyName; // 贸易执行企业
  orderInfoForm.value.executorCompanyCode = option.executorCompanyCode;
  // 选择关联订单后，查询订单详情接口获取商品信息传给商品表格
  orderInfoFun(option.value);
};
const orderInfoFun = async (id: string) => {
  const itemRecord = await infoSalesListApi({ id });
  // 去掉id字段
  const processedData = itemRecord.salesOrderItemVOS
    ? itemRecord.salesOrderItemVOS.map(({ id: _id, ...rest }) => ({
        ...rest,
        originalQuantity: rest.quantity,
        returnQuantity: rest.quantity,
      }))
    : [];
  productGridRef.value.setProductData(processedData);
};
const save = async (type: string) => {
  await formRef.value.validate();
  orderInfoForm.value.salesReturnOrderItem = productGridRef.value?.getProductData() || [];
  orderInfoForm.value.status = type;
  changeOkLoading(true);
  let api = addSalesReturnListApi;
  if (orderInfoForm.value.id) {
    api = editSalesReturnListApi;
  }
  try {
    const res = await api(orderInfoForm.value as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" @click="save('DRAFTING')">保存</a-button>
        <a-button type="primary" @click="save('SUBMITTED')">提交</a-button>
      </a-space>
    </template>
    <a-form
      ref="formRef"
      :colon="false"
      :model="orderInfoForm"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="销售退货单编号" name="salesReturnOrderCode">
            <a-input v-model:value="orderInfoForm.salesReturnOrderCode" disabled v-if="orderInfoForm.id" />
            <a-input value="自动生成" disabled v-else />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="销售退货订单名称" name="salesReturnOrderName">
            <a-input v-model:value="orderInfoForm.salesReturnOrderName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="关联销售订单" name="salesOrderId">
            <ApiComponent
              v-model="orderInfoForm.salesOrderId"
              :component="Select"
              :api="getOrderListApi"
              :params="{ type: 'sales' }"
              label-field="salesOrderName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleCodesSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目名称" name="projectName">
            <a-input v-model:value="orderInfoForm.projectName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目编号" name="projectCode">
            <a-input v-model:value="orderInfoForm.projectCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="贸易执行企业" name="executorCompanyName">
            <a-input v-model:value="orderInfoForm.executorCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="下游企业" name="purchaserCompanyName">
            <a-input v-model:value="orderInfoForm.purchaserCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="任务类型" name="taskType">
            <a-select v-model:value="orderInfoForm.taskType" :options="dictStore.getDictList('TASK_TYPE')" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="orderInfoForm.remarks" :rows="4" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 业务信息 -->
      <BasicCaption content="业务信息" />
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="业务日期" name="businessDate">
            <a-date-picker
              v-model:value="orderInfoForm.businessDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预计结束日期" name="estimatedEndDate">
            <a-date-picker
              v-model:value="orderInfoForm.estimatedEndDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <ProductInfo ref="productGridRef" :props-data="orderInfoForm" @emit-reset="orderInfoFun" />
      <BaseAttachmentList
        :key="orderInfoForm.id"
        v-model="orderInfoForm.attachmentList"
        :business-id="orderInfoForm.id"
        business-type="SCM_SALESRETURN_ORDER"
        edit-mode
      />
    </a-form>
  </BasicPopup>
</template>
