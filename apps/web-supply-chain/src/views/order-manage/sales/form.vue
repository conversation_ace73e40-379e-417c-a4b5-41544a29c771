<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message, Select } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addSalesListApi,
  editSalesListApi,
  getCompanyRecordApi,
  getOrderCodesApi,
  getProjectListApi,
  getUserListApi,
  infoPurchaseListApi,
  infoSalesListApi,
} from '#/api';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);
const dictStore = useDictStore();
const colSpan = { md: 12, sm: 24 };
const orderInfoForm = ref<OrderInfo>({});
const formRef = ref();
const userList = ref();
const rules: Record<string, Rule[]> = {
  salesOrderName: [{ required: true, message: '请输入销售订单名称', trigger: 'blur' }],
  projectName: [{ required: true, message: '请选择所属项目名称', trigger: 'change' }],
  purchaseOrderId: [{ required: true, message: '请选择关联销售订单', trigger: 'change' }],
  purchaserCompanyName: [{ required: true, message: '请选择下游企业', trigger: 'change' }],
  businessDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  estimatedEndDate: [{ required: true, message: '请选择预计结束日期', trigger: 'change' }],
  depositRatio: [{ required: true, message: '请输入保证金比例', trigger: 'blur' }],
  depositAmount: [{ required: true, message: '请输入保证金金额', trigger: 'blur' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'blur' }],
  advanceRatio: [{ required: true, message: '请输入垫资比例', trigger: 'blur' }],
};
const title = computed(() => {
  return orderInfoForm.value.id ? '编辑销售订单' : '新增销售订单';
});

const productGridRef = ref();
const init = async (data: OrderInfo) => {
  userList.value = await getUserListApi();
  if (data.id) {
    orderInfoForm.value = await infoSalesListApi({ id: data.id });
    productGridRef.value.setProductData(orderInfoForm.value.salesOrderItemVOS);
  } else {
    orderInfoForm.value = data;
  }
};
// 处理项目选择事件的函数
const handleProjectSelect = (_value: string, option: any) => {
  orderInfoForm.value.projectId = option.id; // 项目ID
  orderInfoForm.value.projectName = option.label; // 项目编号
  orderInfoForm.value.projectCode = option.value; // 项目编号
  orderInfoForm.value.executorCompanyName = option.executorCompanyName; // 贸易执行企业名称
  orderInfoForm.value.executorCompanyCode = option.executorCompanyCode; // 贸易执行企业代码
  // orderInfoForm.value.businessManagerId = option.businessManagerId; // 业务负责人ID
  // orderInfoForm.value.businessManagerName = option.businessManagerName; // 业务负责人名称
  // orderInfoForm.value.operationManagerId = option.operationManagerId; // 运营负责人ID
  // orderInfoForm.value.operationManagerName = option.operationManagerName; // 运营负责人名称
  // PURCHASE,SALE
  orderInfoForm.value.businessStructure = option.businessStructure; // 业务结构
  orderInfoForm.value.projectModel = option.projectModel; // 项目模式 (建材模式, 产业模式等)
  orderInfoForm.value.businessManagerName = userNameHandler(option.businessManagerId);
  orderInfoForm.value.operationManagerName = userNameHandler(option.operationManagerId);
};
const userNameHandler = (idList: string[]) =>
  idList
    .map((value: string) => {
      const user = userList.value.find((item: { code: string }) => item.code === String(value));
      return user ? user.realName : value;
    })
    .join(', ');
// 处理关联订单选择事件的函数
const handleCodesSelect = async (_value: string, option: any) => {
  orderInfoForm.value.purchaseOrderId = option.value;
  orderInfoForm.value.purchaseOrderName = option.label;
  orderInfoForm.value.purchaseOrderCode = option.orderCode;
  // 选择关联订单后，查询订单详情接口获取商品信息传给商品表格
  orderInfoFun(option.value);
};
const orderInfoFun = async (id: string) => {
  const itemRecord = await infoPurchaseListApi({ id });
  // 去掉id字段
  const processedData = itemRecord.purchaseOrderItemVOS
    ? itemRecord.purchaseOrderItemVOS.map(({ id: _id, ...rest }) => rest)
    : [];
  productGridRef.value.setProductData(processedData);
};

const save = async (type: string) => {
  await formRef.value.validate();
  const productRecord = productGridRef.value?.getProductData() || [];
  orderInfoForm.value.salesOrderItemRequestS = productRecord.items;
  orderInfoForm.value.totalAmountWithTax = productRecord.totalAmountWithTax;
  orderInfoForm.value.totalTaxAmount = productRecord.totalTaxAmount;
  orderInfoForm.value.totalAmountWithoutTax = productRecord.totalAmountWithoutTax;
  orderInfoForm.value.status = type;
  changeOkLoading(true);
  let api = addSalesListApi;
  if (orderInfoForm.value.id) {
    api = editSalesListApi;
  }
  try {
    const res = await api(orderInfoForm.value as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const businessStructureType = computed(() => {
  return dictStore.formatter(orderInfoForm.value.businessStructure, 'BUS_STRUCTURE') as string;
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" @click="save('DRAFTING')">保存</a-button>
        <a-button type="primary" @click="save('SUBMITTED')">提交</a-button>
      </a-space>
    </template>
    <a-form
      ref="formRef"
      :colon="false"
      :model="orderInfoForm"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="销售订单编号" name="salesOrderCode">
            <a-input v-model:value="orderInfoForm.salesOrderCode" disabled v-if="orderInfoForm.id" />
            <a-input value="自动生成" disabled v-else />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="销售订单名称" name="salesOrderName">
            <a-input v-model:value="orderInfoForm.salesOrderName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目名称" name="projectName">
            <ApiComponent
              v-model="orderInfoForm.projectName"
              :component="Select"
              :api="getProjectListApi"
              label-field="projectName"
              value-field="projectCode"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleProjectSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目编号" name="projectCode">
            <a-input v-model:value="orderInfoForm.projectCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务结构" name="businessStructure">
            <a-input v-model:value="businessStructureType" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目模式" name="projectModel">
            <a-input :value="dictStore.formatter(orderInfoForm.projectModel, 'PROJECT_MODE')" disabled />
          </a-form-item>
        </a-col>

        <a-col v-bind="colSpan" v-if="businessStructureType === '先采后销'">
          <a-form-item label="关联采购订单" name="purchaseOrderId">
            <ApiComponent
              v-model="orderInfoForm.purchaseOrderId"
              :component="Select"
              :api="getOrderCodesApi"
              :params="{ orderType: 'purchaseOrder' }"
              label-field="orderName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleCodesSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="businessStructureType === '先采后销'">
          <a-form-item label="采购订单编号" name="purchaseOrderCode">
            <a-input v-model:value="orderInfoForm.purchaseOrderCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="贸易执行企业" name="executorCompanyName">
            <a-input v-model:value="orderInfoForm.executorCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="下游企业" name="purchaserCompanyName">
            <ApiComponent
              v-model="orderInfoForm.purchaserCompanyName"
              :component="Select"
              :api="getCompanyRecordApi"
              label-field="companyName"
              value-field="companyCode"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="
                (_value: string, option: any) => {
                  orderInfoForm.purchaserCompanyName = option.label;
                  orderInfoForm.purchaserCompanyCode = option.value;
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务负责人" name="businessManagerName">
            <a-input v-model:value="orderInfoForm.businessManagerName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="运营负责人" name="operationManagerName">
            <a-input v-model:value="orderInfoForm.operationManagerName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务日期" name="businessDate">
            <a-date-picker
              v-model:value="orderInfoForm.businessDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预计结束日期" name="estimatedEndDate">
            <a-date-picker
              v-model:value="orderInfoForm.estimatedEndDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="保证金比例(%)" name="depositRatio">
            <a-input-number v-model:value="orderInfoForm.depositRatio" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item name="depositAmount">
            <template #label>
              保证金金额
              <VbenTooltip title="默认保证金金额=销售订单总金额*保证金比例">
                <VbenIcon icon="ant-design:question-circle-outlined" class="mr-1 cursor-pointer text-base" />
              </VbenTooltip>
            </template>
            <a-input-number v-model:value="orderInfoForm.depositAmount" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="businessStructureType === '先销后采'">
          <a-form-item label="账期（天)" name="paymentTermDays">
            <a-input-number v-model:value="orderInfoForm.paymentTermDays" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="截止交货日期" name="deliveryDeadline">
            <a-date-picker
              v-model:value="orderInfoForm.deliveryDeadline"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="businessStructureType === '先销后采'">
          <a-form-item label="垫资比例(%)" name="advanceRatio">
            <a-input-number v-model:value="orderInfoForm.advanceRatio" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="任务类型" name="taskType">
            <a-select v-model:value="orderInfoForm.taskType" :options="dictStore.getDictList('TASK_TYPE')" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="orderInfoForm.remarks" :rows="4" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <ProductInfo
        ref="productGridRef"
        :form-props="{ ...orderInfoForm, businessStructureType }"
        @order-info-fun="orderInfoFun"
      />
      <BaseAttachmentList
        :key="orderInfoForm.id"
        v-model="orderInfoForm.attachmentList"
        :business-id="orderInfoForm.id"
        business-type="SCM_SALES_ORDER"
        edit-mode
      />
    </a-form>
  </BasicPopup>
</template>
