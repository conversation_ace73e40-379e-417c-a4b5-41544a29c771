import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
import type { ProjectPageParams, ProjectBaseInfo } from '#/api';

// 项目分页查询
export async function projectManagePageApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/manage/page', { params });
}

// 项目详情查看
export async function projectManageDetailApi(id: number) {
  return requestClient.get(`/scm/project/manage/detail/${id}`);
}

// 项目信息删除
export async function projectManageDeleteApi(id: number) {
  return requestClient.post(`/scm/project/manage/delete/${id}`);
}

// 变更项目信息
export async function projectManageChangeApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/manage/change', data);
}

// 项目信息作废
export async function projectManageCancelApi(id: number) {
  return requestClient.post(`/scm/project/manage/cancel/${id}`);
}

// 项目条件查询
export async function projectManageListApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/manage/list', { params });
}
