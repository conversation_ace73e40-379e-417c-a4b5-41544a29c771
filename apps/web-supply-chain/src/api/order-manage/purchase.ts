import type { RequestClientConfig } from '@vben/request';
import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 产品信息
export interface OrderProductInfo {
  id?: string;
  // 状态
  status?: string;
  // 商品名称
  productName?: string;
  // 商品别名
  productAlias?: string;
  // 规格型号
  skuCode?: string;
  // 商品编码
  spuCode?: string;
  // 计量单位
  measureUnit?: string;
  // 商品品牌
  brandName?: string;
  // 生产厂家
  manufacturerName?: string;
  // 采购数量
  quantity?: string;
  // 采购退货数量
  returnQuantity?: string;
  // 含税单价
  priceWithTax?: string;
  // 税率(%)
  taxRate?: string;
  // 含税金额
  amountWithTax?: string;
  // 税额
  taxAmount?: string;
  // 不含税金额
  amountWithoutTax?: string;
  // 不含税单价
  priceWithoutTax?: string;
  // 采购订单行号
  purchaseOrderCode?: string;
  // 备注
  remarks?: string;
  // 仓库名称
  warehouseName?: string;
  purchasePrice?: string;
}

// 订单关联企业信息
export interface OrderCompanyInfo {
  id?: string;
  supplierCompanyCode?: string; // 上游企业/供应商名称
  supplierCompanyName?: string;
  purchaserCompanyName?: string; // 下游企业/客户名称
  purchaserCompanyCode?: string;
  partnerType?: string;
  companyName?: string;
  companyCode?: string;
}
// 订单信息接口扩展
export interface OrderInfo extends OrderCompanyInfo, OrderProductInfo {
  id?: string;
  // DRAFT("draft","草稿"),
  // SUBMITTED("submitted","已提交"),
  // EFFECT("effect", "已生效"),
  // CANCEL("cancel","已作废");
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 所属项目ID
  projectId?: string;
  // 上游企业/供应商ID
  supplierId?: string;
  // 关联采购订单ID
  purchaseOrderId?: string;
  // 采购订单编号
  purchaseOrderCode?: string;
  // 采购订单名称
  purchaseOrderName?: string;
  // 销售订单名称
  salesOrderName?: string;
  // 上游企业/供应商名称
  supplierCompanyName?: string;
  // 上游企业ID
  supplierCompanyId?: string;
  // 下游企业
  purchaserName?: string;
  // 下游企业

  purchaserCompanyName?: string;
  // 贸易执行企业名称
  executorCompanyName?: string;
  executorCompanyCode?: string;
  // 所属项目名称
  projectName?: string;
  // 所属项目编号
  projectCode?: string;
  // 业务结构 (SELL_FIRST_BUY_LATER, BUY_FIRST_SELL_LATER)
  businessStructure?: string;
  // 项目模式 (建材模式, 产业模式等)
  projectModel?: string;
  // 关联销售订单ID (用于"先销后采"模式)
  salesOrderId?: string;
  // 关联销售订单编号
  salesOrderCode?: string;
  // 业务日期
  businessDate?: string;
  // 预计结束日期
  estimatedEndDate?: string;
  // 预付款比例(%)
  prepaymentRatio?: string;
  // 预付款金额
  prepaymentAmount?: string;
  // 备注
  remarks?: string;
  // 业务负责人名称
  businessManagerName?: string;
  businessManagerId?: string;
  // 运营负责人名称
  operationManagerName?: string;
  operationManagerId?: string;
  // 保证金比例
  depositRatio?: string;
  // 保证金金额
  depositAmount?: string;
  // 垫资比例(%)
  advanceRatio?: string;
  // 账期天数（天)
  paymentTermDays?: string;

  // 采购退货单编号
  purchaseReturnOrderCode?: string;
  //  采购退货单名称
  purchaseReturnOrderName?: string;
  //  销售退货单编号
  salesReturnOrderCode?: string;
  //  销售退货单名称
  salesReturnOrderName?: string;

  // 采购商品提交list
  purchaseOrderItemRequests?: OrderProductInfo[];
  // 销售商品提交list
  salesOrderItemRequestS?: OrderProductInfo[];
  // 采购退货商品提交list
  purchaseReturnOrderItemBOS?: OrderProductInfo[];
  // 销售退货商品提交list
  salesReturnOrderItem?: OrderProductInfo[];

  // 采购商品详情list
  purchaseOrderItemVOS?: OrderProductInfo[];
  // 销售商品详情list
  salesOrderItemVOS?: OrderProductInfo[];
  // 采购退货商品详情list
  purchaseReturnOrderItemVOS?: OrderProductInfo[];
  // 销售退货商品详情list
  salesReturnOrderItemVOS?: OrderProductInfo[];

  // 任务类型
  taskType?: string;
  // 变更状态
  orderState?: string;
  // 订单类型
  orderType?: string;
  // 订单编号
  orderCode?: string;
  // 订单名称
  orderName?: string;
  // 订单状态
  state?: string;
  createTime?: string;
  updateTime?: string;
  totalAmountWithTax?: string;
  totalTaxAmount?: string;
  totalAmountWithoutTax?: string;
  projectPartners?: OrderCompanyInfo[];
  businessStructureType?: string;
  attachmentList?: any;
  deliveryDeadline?: string;
  isCompleted?: number;
  type?: string;
}

export interface QueryGoodsRequest extends OrderCompanyInfo {
  projectId?: number;
  ids?: string[];
}

export async function getPurchaseListApi(params: PageListParams) {
  return requestClient.get('/scm/order/purchase/list', { params });
}
export async function addPurchaseListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/purchase/save', data);
}
export async function editPurchaseListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/purchase/update', data);
}
export async function infoPurchaseListApi(params: OrderInfo) {
  return requestClient.get<OrderInfo>('/scm/order/purchase/detail', { params });
}
export async function changePurchaseApi(params: OrderInfo) {
  // CANCEL：作废   DRAFT：变更
  return requestClient.post<OrderInfo>('/scm/order/purchase/change', params);
}
// 查询项目列表
export async function getProjectListApi(params: OrderInfo) {
  return requestClient.get('/scm/project/manage/list', { params });
  // return requestClient.get('/scm/project/proposal/list', { params });
}
export async function delPurchaseApi(id: string) {
  return requestClient.post(`/scm/order/purchase/delete?id=${id}`);
}

// 查询关联订单列表  salesOrder 销售; purchaseOrder 采购
export async function getOrderCodesApi(params: OrderInfo) {
  return requestClient.post('/scm/order/base/codeLists', params);
}
// 查询商品
export async function getProductListApi(params: PageListParams) {
  return requestClient.get('/base/product/material/page', { params });
}
// 查询企业列表
export async function getCompanyRecordApi(params: PageListParams) {
  return requestClient.get('/base/company/list', { params });
}
export async function importProductApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/scm/order/purchase/upload', data, config);
}
export async function downloadProductTemplateApi() {
  return requestClient.downloadAndSave('/scm/order/purchase/download');
}
export async function exportProductApi(data: OrderProductInfo[], type = 'purchase') {
  return requestClient.downloadAndSave(`/scm/order/${type}/export`, {
    config: { params: { itemList: JSON.stringify(data) } },
  });
}

// 入库商品查询
export async function getPurchaseGoodsApi(params: QueryGoodsRequest) {
  return requestClient.post('/scm/order/purchase/queryItem', params);
}
// 查询订单列表全部数据
export async function getOrderListApi(params: OrderInfo) {
  return requestClient.get(`/scm/order/${params.type}/orderList`);
}
