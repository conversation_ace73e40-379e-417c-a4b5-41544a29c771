<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ContractInfo } from '#/api';

import { computed, nextTick, ref } from 'vue';

import {OnlyOffice, StatusTag} from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, DETAIL_GRID_OPTIONS, FORM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { BaseFileList, BaseFilePickList } from '#/adapter/base-ui';
import { getContractSignInfoApi, getOnlyOfficeFileInfoApi, saveOnlyOfficeFileApi, submitContractSignApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const init = async (data: ContractInfo) => {
  contractForm.value = data.id
    ? await getContractSignInfoApi({ id: data.id })
    : {
        ...data,
      };
  await gridApi.grid.reloadData(contractForm.value.signDetailList ?? []);
  await nextTick();
  EditorRef.value.init(contractForm.value.contractFileId, 'edit');
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const modalTitle = ref('新增合同');
const ContractFormRef = ref();
const EditorRef = ref();
const contractForm = ref<ContractInfo>({});
const completedDate = computed({
  get() {
    return contractForm.value.completedDate;
  },
  set(value: string) {
    if (value) {
      contractForm.value.completedDate = Number(value);
    }
  },
});
const rules = {
  completedDate: [{ required: true, message: '请选择完成签约时间', trigger: 'change' }],
  fileCompleteId: [{ required: true, message: '请上传签约文件', trigger: 'change' }],
};
const colSpanProp = COL_SPAN_PROP;
const save = async () => {
  await ContractFormRef.value.validate();
  changeOkLoading(true);
  try {
    const formData = cloneDeep(contractForm.value);
    const res = await submitContractSignApi(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const signGridOptions = {
  columns: [
    {
      field: 'signerType',
      title: '类型',
      minWidth: '100px',
      formatter: ['formatStatus', 'SIGN_USER_TYPE'],
    },
    {
      field: 'signatoryParam',
      title: '签约角色',
      minWidth: '160px',
    },
    {
      field: 'signInfo',
      title: '签约方信息',
      slots: { default: 'sign_info' },
      minWidth: '500px',
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: signGridOptions,
});
</script>

<template>
  <BasicPopup :title="modalTitle" show-ok-btn @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="ContractFormRef" class="" :model="contractForm" :rules="rules" v-bind="FORM_PROP">
        <a-row class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同名称" name="contractName">
              {{ contractForm.contractName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同编码" name="contractCode">
              {{ contractForm.contractCode }}
            </a-form-item>
          </a-col>
          <a-descriptions-item label="用章类型">
            <StatusTag code="SEAL_TYPE" :value="contractForm.sealType" />
          </a-descriptions-item>
          <a-descriptions-item label="合同分类">
            {{ contractForm.categoryName }}
          </a-descriptions-item>
          <!--<a-col v-bind="colSpanProp">-->
          <!--  <a-form-item label="签约类型" name="contractCode">-->
          <!--   <StatusTag :status="contractForm.signMethod" code="todo" />-->
          <!--  </a-form-item>-->
          <!--</a-col>-->
          <a-col v-bind="colSpanProp">
            <a-form-item label="完成签约时间" name="completedDate">
              <a-date-picker v-model:value="completedDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="设置签约方" />
        <Grid>
          <template #sign_info="{ row }">
            <a-space>
              <span v-if="row.signerType === '1'">{{ row.signatoryOrg }}</span>
              <span>{{ row.signatoryName }}</span>
              <span>{{ row.signatoryAccount }}</span>
            </a-space>
          </template>
        </Grid>
        <BasicCaption content="合同文件">
          <template v-if="contractForm.createType === '2'" #action>
            <a-button type="primary">保存</a-button>
          </template>
        </BasicCaption>
        <a-row v-if="contractForm.createType === '1'" class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同（源文件）" name="fileId">
              <BaseFileList :model-value="contractForm.fileId" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同（源文件）" name="fileCompleteId">
              <BaseFilePickList v-model="contractForm.fileCompleteId" />
            </a-form-item>
          </a-col>
        </a-row>
        <template v-else-if="contractForm.createType === '2'">
          <OnlyOffice ref="EditorRef" :get-config-api="getOnlyOfficeFileInfoApi" :save-api="saveOnlyOfficeFileApi" />
        </template>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
