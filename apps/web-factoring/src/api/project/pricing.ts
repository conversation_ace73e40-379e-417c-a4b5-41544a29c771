import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PricingInfo {
  /**
   * 区域特性
   */
  areaCharacteristics?: string;
  /**
   * 底层项目
   */
  bottomProject?: string;
  calculation?: ProjectPricingCalculationBO;
  /**
   * 是否审批（变更）
   */
  changeIsReview?: number;
  /**
   * 额度测算结果（元）
   */
  creditCalculateAmount?: number;
  /**
   * 客户类别
   */
  customerCategory?: string;
  /**
   * 客户分级
   */
  customerLevel?: string;
  /**
   * 担保方式描述
   */
  guaranteeMethodDesc?: string;
  /**
   * 还本付息试算历史列表
   */
  historyCalculationList?: ProjectPricingCalculationBO[];
  /**
   * 主键
   */
  id?: number;
  /**
   * 行业属性
   */
  industryAttributes?: string;
  /**
   * 是否是变更详情
   */
  isChange?: boolean;
  /**
   * 是否审批（复核）
   */
  isReview?: number;
  /**
   * 基准定价（%）
   */
  pricingBasicRatio?: number;
  /**
   * 授信额度（元）
   */
  pricingCreditAmount?: number;
  /**
   * 授信费率（%）
   */
  pricingCreditRate?: number;
  /**
   * 授信期限（月）
   */
  pricingCreditTerm?: number;
  /**
   * 是否循环额度（授信方式）
   */
  pricingCreditType?: string;
  /**
   * 复核定价方案说明
   */
  pricingDesc?: string;
  /**
   * 复核浮动定价（%）
   */
  pricingFloatingRatio?: number;
  /**
   * 项目定价名称
   */
  pricingName?: string;
  /**
   * 其他情况说明
   */
  pricingOtherDesc?: string;
  /**
   * 综合收益率（%/年）
   */
  pricingXirrRate?: number;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 审批状态（复核/变更）
   */
  reviewStatus?: string;
  /**
   * 定价操作状态
   */
  status?: string;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: number;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  [property: string]: any;
}

/**
 * ProjectPricingCalculationBO，项目还本付息试算
 */
export interface ProjectPricingCalculationBO {
  /**
   * 试算明细
   */
  detailList?: ProjectPricingCalculationDetailBO[];
  /**
   * 预估最后还款日
   */
  expectedDueDate?: Date;
  /**
   * 预估计息天数（天）
   */
  expectedInterestDays?: number;
  /**
   * 预计业务投放日
   */
  expectedLaunchDate?: Date;
  /**
   * 预估还款期数
   */
  expectedRepayPeriods?: number;
  /**
   * 计划融资金额（元）
   */
  financingAmount?: number;
  /**
   * 融资比例（%）
   */
  financingRatio?: number;
  /**
   * 宽限期天数（天）
   */
  gracePeriodDays?: number;
  /**
   * 宽限期费率（%/年）
   */
  gracePeriodRate?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 分期还息频次
   */
  interestPeriod?: string;
  /**
   * 还息方式
   */
  interestRepaymentMethod?: string;
  /**
   * 合同利率（%/年）
   */
  nominalInterestRate?: number;
  /**
   * 固定罚息利率（%）
   */
  penaltyInterestRate?: number;
  /**
   * 阶梯罚息规则（JSON格式）
   */
  penaltySteps?: string;
  /**
   * 罚息类型：固定利率/阶梯利率
   */
  penaltyType?: string;
  /**
   * 还本付息计划规划方式
   */
  planningMethod?: string;
  /**
   * 定价日志ID（历史版本有）
   */
  pricingLogId?: number;
  /**
   * 试算版本：当前/历史
   */
  pricingVersion?: string;
  /**
   * 分期还本频次
   */
  principalPeriod?: string;
  /**
   * 还本方式
   */
  principalRepaymentMethod?: string;
  /**
   * 项目编码
   */
  projectCode?: string;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目定价ID
   */
  projectPricingId?: number;
  /**
   * 默认当期还息日
   */
  repayInterestDay?: string;
  /**
   * 默认当期还本日
   */
  repayPrincipalDay?: string;
  /**
   * 服务费（元）
   */
  serviceFeeAmount?: number;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: number;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  /**
   * 测算综合收益率（%/年）
   */
  xirrRate?: number;
  [property: string]: any;
}

/**
 * ProjectPricingCalculationDetailBO，项目还本付息试算明细
 */
export interface ProjectPricingCalculationDetailBO {
  /**
   * 当期还本/付息日
   */
  currentDate?: Date;
  /**
   * 应还宽限期利息(元)
   */
  graceInterestAmount?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应还利息(元)
   */
  interestAmount?: number;
  /**
   * 应还逾期罚息(元)
   */
  overdueInterestAmount?: number;
  /**
   * 应还本金(元)
   */
  principalAmount?: number;
  /**
   * 试算ID
   */
  projectPricingCalculationId?: number;
  /**
   * 还款项
   */
  repaymentItem?: string;
  /**
   * 还款期数
   */
  repayPeriods?: number;
  /**
   * 应收服务费(元)
   */
  serviceAmount?: number;
  /**
   * 当期净现金流(元)
   */
  totalAmount?: number;
  [property: string]: any;
}
// 获取定价分页列表
export async function getPricingPageListApi(params: PageListParams) {
  return requestClient.get<PricingInfo[]>('/factoring/project/pricing/page', { params });
}

// 添加综合定价
export async function addComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/add', data);
}

// 编辑综合定价
export async function editComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/edit', data);
}

// 复核综合定价
export async function recheckComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/recheck/edit', data);
}

// 变更综合定价
export async function changeComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/change/edit', data);
}

// 添加单一定价
export async function addSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/add', data);
}

// 编辑单一定价
export async function editSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/edit', data);
}

// 复核单一定价
export async function recheckSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/recheck/edit', data);
}

// 变更单一定价
export async function changeSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/change/edit', data);
}

// 定价变更状态
export async function getPricingChangeStatusApi(id: number) {
  return requestClient.post(`/factoring/project/pricing/change/${id}`);
}

// 获取定价详情
export async function getPricingInfoApi(id: number) {
  return requestClient.get(`/factoring/project/pricing/detail/${id}`);
}

// 删除定价
export async function delPricingApi(id: number) {
  return requestClient.post(`/factoring/project/pricing/delete/${id}`);
}

// 驳回
export async function rejectRecheckPricingApi(id: number) {
  return requestClient.post(`/factoring/project/pricing/recheck/reject/${id}`);
}

// 试算
export async function calculationPricing(data: ProjectPricingCalculationBO) {
  return requestClient.post<ProjectPricingCalculationBO>('/factoring/project/pricing/start', data);
}

// 仅更新利息
export async function calculationPricingInterest(data: ProjectPricingCalculationBO) {
  return requestClient.post<ProjectPricingCalculationBO>('/factoring/project/pricing/start/interest', data);
}

// 试算明细导出
export async function calculationPricingApi(id: number) {
  return requestClient.downloadAndSave(`/factoring/project/pricing/export?id=${id}`);
}

// 试算历史详情
export async function getPricingCalculationDetailApi(id: number) {
  return requestClient.get(`/factoring/project/pricing/calculation/detail/${id}`);
}

// 获取定价列表
export async function getPricingListApi() {
  return requestClient.get('/factoring/project/pricing/list');
}

// 获取定价详情
export async function getPricingByProjectIdApi(id: number) {
  return requestClient.get(`/factoring/project/pricing/detailByProjectId/${id}`);
}

// 试算定价变更历史详情
export async function getPricingLogDetailApi(id: number) {
  return requestClient.get(`/factoring/project/pricing/log/detail/${id}`);
}
