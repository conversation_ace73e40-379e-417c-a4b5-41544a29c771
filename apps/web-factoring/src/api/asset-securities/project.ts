// ABS项目信息接口扩展
import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface AbsProjectInfo extends BaseDataParams {
  /**
   * ABS项目简介
   */
  absProjectSummary?: string;
  /**
   * 会计师事务所
   */
  accountingFirm?: string;
  /**
   * 审批备案情况
   */
  approvalFilingSummary?: string;
  /**
   * 基础资产
   */
  baseAsset?: string;
  /**
   * 增信措施
   */
  creditEnhancementMethod?: string;
  /**
   * 信用风险评估
   */
  creditRiskSummary?: string;
  /**
   * 托管银行
   */
  custodianBank?: string;
  /**
   * 申报场所
   */
  declarationPlace?: string;
  /**
   * 募集资金用途
   */
  declarationPurpose?: string;
  /**
   * 申报融资规模
   */
  declareFinancingScale?: number;
  /**
   * 立项日期
   */
  establishDate?: Date;
  /**
   * 融资期限（年）
   */
  financeTermYear?: string;
  /**
   * 担保人统一社会信用代码
   */
  guaranteeCode?: string;
  /**
   * 担保人
   */
  guaranteeName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 律师事务所
   */
  lawFirm?: string;
  /**
   * 法律法规依据
   */
  legalBasis?: string;
  /**
   * 市场风险评估
   */
  marketRiskSummary?: string;
  /**
   * 操作风险评估
   */
  operationalRiskSummary?: string;
  /**
   * 原始受益人统一社会信用代码
   */
  originalBeneficiaryCode?: string;
  /**
   * 原始受益人
   */
  originalBeneficiaryName?: string;
  /**
   * 计划管理人
   */
  planManager?: string;
  /**
   * 产品分层
   */
  productLayer?: string;
  /**
   * ABS项目编号
   */
  projectCode?: string;
  /**
   * 项目负责人
   */
  projectManager?: string;
  /**
   * ABS项目名称
   */
  projectName?: string;
  /**
   * 评级机构
   */
  ratingAgency?: string;
  /**
   * 还息方式
   */
  returnInterestMethod?: string;
  /**
   * 还本方式
   */
  returnMethod?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 操作状态
   */
  status?: string;
  [property: string]: any;
}

// 获取ABS项目分页列表
export async function getAbsProjectPageListApi(params: PageListParams) {
  return requestClient.get<AbsProjectInfo[]>('/factoring/abs/project/page', { params });
}

// 添加ABS项目
export async function addAbsProjectApi(data: AbsProjectInfo) {
  return requestClient.post<AbsProjectInfo>('/factoring/abs/project/add', data);
}

// 编辑ABS项目
export async function editAbsProjectApi(data: AbsProjectInfo) {
  return requestClient.post<AbsProjectInfo>('/factoring/abs/project/edit', data);
}

// 获取ABS项目详情
export async function infoAbsProjectApi(params: AbsProjectInfo) {
  return requestClient.get<AbsProjectInfo>('/factoring/abs/project/info', { params });
}

// 获取ABS项目列表
export async function absProjectListApi(params: AbsProjectInfo) {
  return requestClient.get<AbsProjectInfo>('/factoring/abs/project/list', { params });
}

// 删除ABS项目
export async function delAbsProjectApi(id: string) {
  return requestClient.post('/factoring/abs/project/delete', {}, { params: { id } });
}
