import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface SupervisePageVO extends BaseDataParams, PageListParams {
  /**
   * 主键
   */
  id?: number;
  /**
   * 最后上传人
   */
  lastUploadBy?: string;
  /**
   * 最后上传时间
   */
  lastUploadDate?: Date;
  /**
   * 运营监督报告名称
   */
  reportName?: string;
  /**
   * 上传运营监督报告文件ID
   */
  supervisionFileId?: number;
  /**
   * 监督季度Q1-Q4
   */
  supervisionQuarter?: string;
  /**
   * 监督年YYYY
   */
  supervisionYear?: string;

  [property: string]: any;
}

export interface SuperviseParams {
  id?: number;

  /**
   * 运营监督报告名称
   */
  reportName: string;
  /**
   * 上传运营监督报告文件ID
   */
  supervisionFileId?: number;
  /**
   * 监督季度Q1-Q4
   */
  supervisionQuarter: string;
  /**
   * 监督年YYYY
   */
  supervisionYear: string;
}

export async function getSupervisePageListApi(params: PageListParams) {
  return requestClient.get<SupervisePageVO[]>('/factoring/operation/supervision/page', { params });
}

export async function addSuperviseApi(data: SuperviseParams) {
  return requestClient.post<string>('/factoring/operation/supervision/add', data);
}

export async function delSuperviseApi(id: string) {
  return requestClient.get(`/factoring/operation/supervision/delete/${id}`);
}

export async function addSuperviseFileApi(data: SuperviseParams) {
  return requestClient.post<string>('/factoring/operation/supervision/re/upload', data);
}
