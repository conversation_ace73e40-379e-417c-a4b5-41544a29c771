import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface EvaluationPageVO extends BaseDataParams, PageListParams {
  /**
   * 上传项目后评价文件ID
   */
  evaluationFileId?: number;
  /**
   * 项目后评价ID
   */
  id?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  projectId?: number | undefined;

  /**
   * 项目后评价名称
   */
  reportName?: string;
  /**
   * 结清日期
   */
  settleDate?: Date | string;

  [property: string]: any;
}

export interface EvaluationVo {
  /**
   * 上传项目后评价文件ID
   */
  evaluationFileId: number | undefined;

  /**
   * 项目ID
   */
  projectId: number | undefined;

  /**
   * 项目后评价名称
   */
  reportName: string;
  /**
   * 结清日期
   */
  settleDate: Date | string;

  [property: string]: any;
}
export async function getEvaluationPageListApi(params: PageListParams) {
  return requestClient.get<EvaluationPageVO[]>('/factoring/project/evaluation/page', { params });
}

export async function addEvaluationApi(data: EvaluationVo) {
  return requestClient.post<string>('/factoring/project/evaluation/add', data);
}

export async function delEvaluationApi(id: string) {
  return requestClient.get(`/factoring/project/evaluation/delete/${id}`);
}

export async function addEvaluationFileApi(data: EvaluationVo) {
  return requestClient.post<string>('/factoring/project/evaluation/re/upload', data);
}
