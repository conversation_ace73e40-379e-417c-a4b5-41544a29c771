<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { ReceivablePoolVO, ReceivableVO } from '#/api';

import { computed, reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { cloneDeep } from '@vben/utils';

import { Button, Col, Form, FormItem, Input, InputNumber, Row, Select, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCompanyListApi, infoReceivableApi } from '#/api';

import Detail from '../enter/detail.vue';
import ReceivableModal from './receivableModal.vue';

const emit = defineEmits(['register', 'ok']);

// 定义模态框显示状态
const modalVisible = ref(false);
const baseFormInfo = reactive<ReceivablePoolVO>({
  bizType: undefined,
  companyList: [],
  companyCodeList: [],
  discountRate: undefined,
  isSubmit: false,
  id: undefined,
  poolCapitalLimit: undefined,
  poolCode: '',
  poolDueDate: undefined,
  poolName: '',
  poolTotalAmount: undefined,
  poolValidity: undefined,
  projectId: [],
  receivableList: [],
  receivablePoolLogsList: [],
  status: undefined,
  thresholdAmount: undefined,
});

const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  companyCodeList: [{ required: true, message: '请选择池保理融资企业', trigger: 'change' }],
  poolName: [{ required: true, message: '请输入应收账款池名称' }],
  discountRate: [{ required: true, message: '请输入折扣率' }],
  thresholdAmount: [{ required: true, message: '请输入“水位线”金额' }],
  poolCapitalLimit: [{ required: true, message: '请输入池融资金额上限' }],
  poolValidity: [{ required: true, message: '请输入应收账款有效期' }],
};
const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});

const formRef = ref();
const handleSave = async (isSubmit = false) => {
  try {
    await formRef.value.validate();
    // 确保获取表格最新数据
    if (poolApi.grid) {
      const poolTableData = poolApi.grid.getTableData();
      baseFormInfo.receivableList = poolTableData.fullData;
    }
    const params = cloneDeep(baseFormInfo);

    if (isSubmit) {
      params.isSubmit = true;
    }
    emit('ok', params);
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};
const isChange = ref(false);
// 添加初始化方法
const init = (data: ReceivablePoolVO) => {
  if (data && Object.keys(data).length > 0) {
    isChange.value = data.type === 'change';
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));

    companyList.value = data.companyCodeList;
    // 初始化表格数据
    if (poolApi.grid && data.receivableList) {
      poolApi.grid.reloadData(data.receivableList);
    }
  } else {
    // 否则重置为初始状态
    Object.assign(baseFormInfo, {
      projectId: [],
      receivableCode: '',
      bizType: undefined,
      companyList: [],
      companyCodeList: [],
      discountRate: undefined,
      isSubmit: false,
      id: undefined,
      poolCapitalLimit: undefined,
      poolCode: '',
      poolDueDate: undefined,
      poolName: '',
      poolTotalAmount: undefined,
      poolValidity: undefined,
      receivableList: [],
      receivablePoolLogsList: [],
      status: undefined,
      thresholdAmount: undefined,
    });
    companyList.value = [];
  }
};

// 打开模态框方法
const openReceivableModel = () => {
  modalVisible.value = true;
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const labelCol = { style: { width: '150px' } };

const gridPoolTable: VxeGridProps<ReceivableVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'receivableName', title: '应收账款名称' },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'bizType', title: '业务类型', formatter: ['formatStatus', 'FCT_FACTORING_TYPE'] },
    { field: 'receivableAmount', title: '应收账款金额（元）' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
    { field: 'action', title: '操作', fixed: 'right', width: 160, slots: { default: 'action' } },
  ],
  data: baseFormInfo.receivableList || [],
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();

const [PoolTable, poolApi] = useVbenVxeGrid({ gridOptions: gridPoolTable });

// 修改删除行方法
const removeReceivable = (index: number) => {
  const $grid = poolApi.grid;
  if ($grid) {
    const tableData = $grid.getTableData();
    $grid.remove(tableData.fullData[index]);
  }
};
const detailReceivable = async (_row: ReceivableVO) => {
  try {
    const res = await infoReceivableApi(_row.id);
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {
    // message.error('删除失败: ' + error.message);
  }
};
const handleReceivableConfirm = (data: ReceivableVO[]) => {
  const $grid = poolApi.grid;
  $grid.insert(data);
};

const companyList = ref([]);

const handleCompanyChange = (value: any) => {
  baseFormInfo.companyList = value.map((item: any) => ({
    companyName: item.label,
    companyCode: item.key,
  }));
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :show-ok-btn="!isChange" :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="baseFormInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="应收款账池编号" name="poolCode">
            <Input disabled v-model:value="baseFormInfo.poolCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="池保理融资企业" name="companyList">
            <ApiComponent
              :disabled="isChange"
              v-model="companyList as unknown as string"
              mode="multiple"
              :component="Select"
              label-in-value="true"
              :api="getCompanyListApi"
              label-field="companyName"
              @change="handleCompanyChange"
              value-field="companyCode"
              model-prop-name="value"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款池名称" name="poolName">
            <Input :disabled="isChange" v-model:value="baseFormInfo.poolName" placeholder="请输入应收账款池名称" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="池规则" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="折扣率（%）" name="discountRate">
            <InputNumber
              style="width: 100%"
              :disabled="isChange"
              v-model:value="baseFormInfo.discountRate"
              placeholder="请输入折扣率"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="“水位线”金额（元）" name="thresholdAmount">
            <InputNumber
              style="width: 100%"
              :disabled="isChange"
              v-model:value="baseFormInfo.thresholdAmount"
              placeholder="请输入“水位线”金额"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="池融资金额上限（元）" name="poolCapitalLimit">
            <InputNumber
              style="width: 100%"
              :disabled="isChange"
              v-model:value="baseFormInfo.poolCapitalLimit"
              placeholder="请输入池融资金额上限"
              :min="0"
              :precision="2"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款有效期（个月）" name="poolValidity">
            <Input :disabled="isChange" v-model:value="baseFormInfo.poolValidity" placeholder="请输入应收账款有效期" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="应收账款池" />
      <PoolTable>
        <template #toolbarTools>
          <Button type="primary" class="mr-2" @click="() => openReceivableModel()">选择应收账款</Button>
        </template>
        <!-- 操作按钮 -->
        <template #action="{ rowIndex, row }">
          <TypographyLink @click="detailReceivable(row)"> 详情</TypographyLink>
          <TypographyLink type="danger" @click="removeReceivable(rowIndex)"> 删除</TypographyLink>
        </template>
      </PoolTable>
      <!--      <BasicCaption content="入池出池置换记录" />-->
      <!--      <ReplacementTable />-->
    </Form>
    <ReceivableModal v-model:is-visible="modalVisible" @confirm="handleReceivableConfirm" />
  </BasicPopup>
  <Detail @register="detailForm" />
</template>

<style scoped></style>
