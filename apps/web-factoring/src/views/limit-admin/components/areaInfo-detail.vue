<script setup lang="ts">
import type { VxeGridProps } from '@vben/plugins/vxe-tablele';

import type { areaComputeVo, limitRuleLogVO, limitRuleVo } from '#/api';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { TypographyLink } from 'ant-design-vue';

import { spanMethod } from './tableSpanUtils';

const { isCompanyDetail } = defineProps({
  isCompanyDetail: {
    type: Boolean,
    default: false,
  },
});

const dictStore = useDictStore();

const baseFormInfo = defineModel<areaComputeVo>({ type: Object, required: true });

const gridOptions: VxeGridProps<limitRuleVo> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 140,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '43%',
    },
    {
      field: 'contractType',
      title: '数值',
      slots: { default: 'contractType-select' },
      width: '25%',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: baseFormInfo.value.limitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};

const logGridOption: VxeGridProps<limitRuleLogVO> = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    {
      field: 'operationDate',
      title: '测算日期',
      width: 140,
    },
    {
      field: 'creditScore',
      title: '区域保理额度测算得分',
    },
    {
      field: 'budgetAmount',
      title: '地方一般公共预算收入（万元）',
    },
    {
      field: 'creditRatio',
      title: '区域授信比例（%）',
    },
    {
      field: 'creditAmount',
      title: '地区保理授信额度（亿元）',
    },
    {
      field: 'operationType',
      title: '测算方式',
      formatter: ['formatStatus', 'FCT_LIMIT_OPERATION_TYPE'],
    },
    {
      field: 'operationBy',
      title: '操作人',
    },
    {
      field: 'adjustSupportingFileId',
      title: '调整佐证材料',
      slots: { default: 'file-down' },
    },
  ],
  data: baseFormInfo.value.limitLogList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const [logGrid, logGridApi] = useVbenVxeGrid({ gridOptions: logGridOption });

defineExpose({
  gridApi,
  logGridApi,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item v-if="isCompanyDetail" label="企业名称">
          {{ baseFormInfo.companyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item v-if="isCompanyDetail" label="统一社会信用代码">
          {{ baseFormInfo.companyCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item v-if="isCompanyDetail" label="客户类别">
          {{ dictStore.formatter(baseFormInfo.companyType, 'FCT_COMPANY_LIMIT_COMPANY_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item v-if="isCompanyDetail" label="客户评级">
          {{ dictStore.formatter(baseFormInfo.companyRating, 'COMPANY_RATING') }}
        </a-descriptions-item>
        <a-descriptions-item label="地市区县">
          {{ baseFormInfo.cityName || '-' }}/{{ baseFormInfo.districtName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="地方一般公共预算收入（元）">
          {{ baseFormInfo.budgetAmount || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="区域内最高企业评级">
          {{ dictStore.formatter(baseFormInfo.companyRating, 'COMPANY_RATING') }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="区域保理额度测算" />
      <Grid>
        <!-- 评级赋分下拉框 -->
        <template #contractType-select="{ row }">
          <TypographyText v-if="row.dictType !== 'input' && row.dictType !== 'number'">
            {{ dictStore.formatter(row.numericalValue, row.dictType) }}
          </TypographyText>
          <TypographyText v-else v-model:value="row.numericalValue">
            {{ row.numericalValue }}
          </TypographyText>
        </template>
      </Grid>
      <BasicCaption content="地区额度测算结果" />
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="地区保理额度测算得分">
          {{ baseFormInfo.creditScore || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="区域授信比例（%）">
          {{ baseFormInfo.creditRatio || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="地区保理授信额度（元）">
          {{ baseFormInfo.creditAmount || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="地区额度测算历史记录" />
      <logGrid>
        <!-- 下载 -->
        <template #file-down="{ row }">
          <Space>
            <TypographyLink type="primary" @click="downLoad(row)"> 下载 </TypographyLink>
          </Space>
        </template>
      </logGrid>
    </div>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
