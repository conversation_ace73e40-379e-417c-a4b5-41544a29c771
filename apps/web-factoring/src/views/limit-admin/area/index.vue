<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { areaComputePageVO, areaComputeVo } from '#/api';

import { ref, watch } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Modal as AModal, Button, Form, FormItem, Input, message, Space, TypographyLink } from 'ant-design-vue';

import { BaseFilePickList, BaseRegionPicker } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addArea<PERSON>pi,
  adjustAreaComputeApi,
  again<PERSON>rea<PERSON><PERSON>,
  delete<PERSON>rea<PERSON>pi,
  editArea<PERSON><PERSON>,
  getAreaComputePageListApi,
  infoAreaApi,
  infoComputeApi,
} from '#/api';

import Detail from './detail.vue';
import Edit from './edit.vue';

const resultForm = ref<Partial<areaComputeVo>>({});
const labelCol = { style: { width: '150px' } };

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地级市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'cityName', title: '地级市', width: 280 },
    { field: 'districtName', title: '区县' },
    { field: 'budgetAmount', title: '上一年度一般公共预算收入（元）' },
    { field: 'creditRatio', title: '区域授信比例（%）', formatter: ['formatStatus', 'COMPANY_RATING'] },
    { field: 'creditAmount', title: '地区保理授信额度（元）' },
    { field: 'status', title: '操作状态', formatter: ['formatStatus', 'FCT_NOT_REVIEW_STATUS'] },
    { field: 'reviewStatus', title: '审批状态', formatter: ['formatStatus', 'COMPANY_RATING'] },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 300,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAreaComputePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

watch(
  [() => resultForm.value.budgetAmount, () => resultForm.value.creditScore],
  ([newBudgetAmount, newCreditScore]) => {
    // 在这里调用您想要执行的方法
    handleValueChange(newBudgetAmount as number, newCreditScore as number);
  },
);

const handleValueChange = async (newBudgetAmount: number, newCreditScore: number) => {
  if (newBudgetAmount && newCreditScore) {
    const res = await infoComputeApi({
      budgetAmount: newBudgetAmount,
      creditScore: newCreditScore,
      companyRating: resultForm.value.companyRating,
    });

    if (res) {
      resultForm.value.creditRatio = res.creditRatio;
      resultForm.value.creditAmount = res.creditAmount;
    }
  }
};

// 打开新增页面
const add = () => {
  openFormPopup(true, {});
};

const edit = async (_row: areaComputePageVO) => {
  const res = await infoAreaApi({ id: _row.id, logId: _row.lastLogId });
  openFormPopup(true, res);
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await resultFormRef.value.validate();
      await adjustAreaComputeApi(resultForm.value as areaComputeVo);
      await modalApi.close();
      await gridApi.reload();
    } catch {
      message.error('操作失败');
    }
  },
});

// 打开新增页面
const ratChange = async (_row: areaComputePageVO) => {
  try {
    const res = await infoAreaApi({ id: _row.id, logId: _row.lastLogId });
    resultForm.value = cloneDeep(res);
    modalApi.open();
  } catch {
    message.error($t('base.resError'));
  }
};

const editSuccess = async (data: areaComputeVo) => {
  try {
    if (data.status === 'EFFECTIVE') {
      await againAreaApi(data);
    } else {
      data.id ? await editAreaApi(data) : await addAreaApi(data);
    }
    message.success($t('base.resSuccess'));
    openFormPopup(false, {});
    await gridApi.formApi.submitForm(); // 刷新列表数据
  } catch {
    message.error($t('base.resError'));
  }
};

const detail = async (_row: areaComputePageVO) => {
  try {
    const res = await infoAreaApi({ id: _row.id, logId: _row.lastLogId });
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {}
};

const del = async (_row: areaComputePageVO) => {
  try {
    AModal.confirm({
      title: '确认删除',
      content: '确认删除此数据？',
      onOk: async () => {
        try {
          await deleteAreaApi(_row.id);
          message.success($t('base.resSuccess'));
          await gridApi.formApi.submitForm(); // 刷新列表数据
        } catch {
          message.error('删除失败');
        }
      },
    });
  } catch {
    message.error('删除失败');
  }
};

const againRat = async (_row: areaComputePageVO) => {
  const res = await infoAreaApi({ id: _row.id, logId: _row.lastLogId });

  openFormPopup(true, res);
};

const resultFormRef = ref();

// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  companyRating: [{ required: true, message: '请选择本次客户层级调整', trigger: 'change' }],
  budgetAmount: [{ required: true, message: '请输入地方一般公共预算收入', trigger: 'change' }],
  adjustSupportingFileId: [{ required: true, message: '请上传调整佐证材料', trigger: 'change' }],
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="ratChange(row)"> 测算结果调整</TypographyLink>
          <TypographyLink v-if="row.status === 'SUBMIT'" @click="edit(row)"> {{ $t('base.edit') }} </TypographyLink>
          <TypographyLink v-if="row.status === 'SUBMIT'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)"> {{ $t('base.detail') }}</TypographyLink>
          <TypographyLink @click="detail(row)"> 审批</TypographyLink>
          <TypographyLink v-if="row.status === 'EFFECTIVE'" @click="againRat(row)">重新测算</TypographyLink>
        </Space>
      </template>
    </Grid>
    <Edit @register="registerForm" @ok="editSuccess" />
    <Detail @register="detailForm" />
    <Modal title="测算结果调整" class="w-[800px]">
      <Form ref="resultFormRef" :model="resultForm" :label-col="labelCol" :rules="rules" :wrapper-col="{ span: 20 }">
        <FormItem label="地级市/区县" name="companyName">
          <BaseRegionPicker
            disabled
            v-model:city="resultForm.cityName"
            v-model:district="resultForm.districtName"
            v-model:district-code="resultForm.districtCode"
          />
        </FormItem>
        <FormItem label="地方一般公共预算收入（元）" name="budgetAmount">
          <Input v-model:value="resultForm.budgetAmount" />
        </FormItem>
        <FormItem label="本次地区保理额度测算得分调整为" name="creditScore">
          <Input v-model:value="resultForm.creditScore" />
        </FormItem>
        <FormItem label="区域授信比例" name="creditRatio">
          <Input disabled v-model:value="resultForm.creditRatio" />
        </FormItem>
        <FormItem label="地区保理授信额度（元）" name="creditAmount">
          <Input disabled v-model:value="resultForm.creditAmount" />
        </FormItem>
        <FormItem label="上传调整佐证材料" name="adjustSupportingFileId">
          <BaseFilePickList v-model="resultForm.adjustSupportingFileId" />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
