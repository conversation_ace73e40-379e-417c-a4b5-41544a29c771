<script setup lang="ts">
import type { areaComputeVo } from '#/api';

import { reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep } from '@vben/utils';

import AreaInfoDetail from '../components/areaInfo-detail.vue';

const baseFormInfo = reactive<areaComputeVo>({
  adjustSupportingFileId: 0,
  budgetAmount: 0,
  cityCode: '',
  cityName: '',
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  districtCode: '',
  isSubmit: false,
  districtName: '',
  id: 0,
  limitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '过往合作履约情况',
      quotaName: '过往合作履约情况',
      dictType: 'FCT_LIMIT_PREVIOUS',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '负面舆情',
      quotaName: '负面舆情：区域内出现国有发债主体的债券违约事件或者频繁的非标融资、银行借款等债务逾期风险事件',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
});

const areaInfoRef = ref();
// 添加初始化方法
const init = (data: areaComputeVo) => {
  if (data && Object.keys(data).length > 0) {
    if (areaInfoRef.value && data.limitRuleList.length > 0) {
      data.limitRuleList = data.limitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.limitRuleList[index].dictType,
        };
      });
      areaInfoRef.value.gridApi.grid?.reloadData(data.limitRuleList);
    } else if (data.limitLogList && data.limitLogList.length > 0) {
      areaInfoRef.value.logGridApi.grid?.reloadData(data.limitLogList);
    }

    Object.assign(baseFormInfo, cloneDeep(data));
  }
};

const [registerPopup] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <AreaInfoDetail ref="areaInfoRef" v-model="baseFormInfo" />
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
