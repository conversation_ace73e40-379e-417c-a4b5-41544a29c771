<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { companyComputePageVO, companyComputeVo, resultComputeVO } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent, Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Modal as AModal, Button, Form, FormItem, Input, message, Select, Space, TypographyLink } from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addCompanyApi,
  adjustCompanyApi,
  againCompanyApi,
  deleteCompanyApi,
  editCompanyApi,
  getAreaComputeApi,
  getCompanyComputePageListApi,
  getCompanyListApi,
  getRatingByCompanyCodeApi,
  infoCompanyApi,
} from '#/api';

import DetailMarket from './detail-market.vue';
import DetailOperation from './detail-operation.vue';
import Detail from './detail.vue';
import Edit from './edit.vue';
import MarketEdit from './marketEdit.vue';

const dictStore = useDictStore();

const resultForm = reactive<resultComputeVO>({
  adjustSupportingFileId: undefined,
  cityCode: '',
  cityName: '',
  companyCode: '',
  companyName: '',
  creditAmount: 0,
  companyRating: '',
  districtCode: '',
  districtName: '',
  id: 0,
});
const addForm = ref<Partial<resultComputeVO>>({
  companyInfo: {},
  companyType: '',
  adjustSupportingFileId: undefined,
  cityCode: '',
  cityName: '',
  companyCode: '',
  companyName: '',
  companyRating: '',
  creditAmount: 0,
  districtCode: '',
  districtName: '',
  id: 0,
  isSubmit: false,
  marketAdjustFactor: 0,
  marketBasicAmount: 0,
  marketCreditAmount: 0,
  marketCreditScore: 0,
  marketDepreciation: 0,
  marketNetProfit: 0,
  operatingAdjustFactor: 0,
  operatingBasicAmount: 0,
  operatingCreditAmount: 0,
  operatingCreditScore: 0,
  regionBudgetAmount: 0,
  regionCreditAmount: 0,
  regionCreditRatio: 0,
  regionCreditScore: 0,
});
const resultFormRef = ref();
const addFormRef = ref();
const labelCol = { style: { width: '150px' } };

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地级市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
    {
      component: 'Select',
      fieldName: 'companyTypeList',
      label: '企业类型',
      componentProps: {
        options: dictStore.getDictList('FCT_COMPANY_LIMIT_COMPANY_TYPE'),
      },
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'companyName', title: '企业名称', width: 280 },
    { field: 'cityName', title: '地级市', width: 80 },
    { field: 'districtName', title: '区县', width: 80 },
    { field: 'companyType', title: '企业类型', formatter: ['formatStatus', 'FCT_COMPANY_LIMIT_COMPANY_TYPE'] },
    { field: 'regionCreditAmount', title: '区域保理授信额度（元）' },
    { field: 'operatingCreditAmount', title: '区域经营性客户授信额度（元）' },
    { field: 'marketCreditAmount', title: '市场化经营客户授信额度（元）' },
    { field: 'creditAmount', title: '最后授信额度测算结果(元)' },
    { field: 'status', title: '操作状态', formatter: ['formatStatus', 'FCT_NOT_REVIEW_STATUS'] },
    { field: 'reviewStatus', title: '审批状态', formatter: ['formatStatus', 'COMPANY_RATING'] },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 300,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCompanyComputePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [marketForm, { openPopup: openMarketFormPopup }] = usePopup();
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const [detailOperationForm, { openPopup: openDetailOperationFormPopup }] = usePopup();
const [detailMarketForm, { openPopup: openDetailMarketFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

const selectCompany = () => {
  addForm.value = {
    companyInfo: {},
    companyType: '',
    adjustSupportingFileId: 0,
    cityCode: '',
    cityName: '',
    companyCode: '',
    companyName: '',
    companyRating: '',
    creditAmount: 0,
    districtCode: '',
    districtName: '',
    id: 0,
    isSubmit: false,
    marketAdjustFactor: 0,
    marketBasicAmount: 0,
    marketCreditAmount: 0,
    marketCreditScore: 0,
    marketDepreciation: 0,
    marketNetProfit: 0,
    operatingAdjustFactor: 0,
    operatingBasicAmount: 0,
    operatingCreditAmount: 0,
    operatingCreditScore: 0,
    regionBudgetAmount: 0,
    regionCreditAmount: 0,
    regionCreditRatio: 0,
    regionCreditScore: 0,
  };
  addModalApi.open();
};

const edit = async (_row: any) => {
  const companyInfo = await infoCompanyApi({ id: _row.id, logId: _row.lastLogId });
  // 使用公司信息获取评级（现在依赖关系正确）
  const companyRating = await getRatingByCompanyCodeApi({
    companyCode: companyInfo.companyCode,
  });
  const companyData = { ...companyInfo, companyRating };
  _row.companyType === 'operation_customer' ? openFormPopup(true, companyData) : openMarketFormPopup(true, companyData);
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await resultFormRef.value.validate();
      await adjustCompanyApi(resultForm);
      await modalApi.close();
      await gridApi.reload();
    } catch {
      message.error('操作失败');
    }
  },
});

const [AddModal, addModalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await addFormRef.value.validate();
      // await adjustAreaComputeApi(resultForm.value as areaComputeVo);
      const rating = await getRatingByCompanyCodeApi({ companyCode: addForm.value.companyCode });
      if (addForm.value.companyType === 'region_customer') {
        await addCompanyApi({
          companyCode: addForm.value.companyCode,
          companyName: addForm.value.companyName,
          cityName: addForm.value.cityName,
          districtName: addForm.value.districtName,
          districtCode: addForm.value.districtCode,
          companyType: addForm.value.companyType,
          companyRating: rating,
        });
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } else if (addForm.value.companyType === 'operation_customer') {
        const res = await getAreaComputeApi({ districtCode: addForm.value.districtCode });
        const params = { ...addForm.value, ...res };
        if (params.id) delete params.id;
        if (params.status) delete params.status;
        openFormPopup(true, params);
      } else {
        const params = { ...addForm.value, companyRating: rating };
        if (params.id) delete params.id;
        if (params.status) delete params.status;
        openMarketFormPopup(true, params);
      }
      await addModalApi.close();
    } catch {
      message.error($t('base.resError'));
    }
  },
});

// 打开新增页面
const ratChange = async (_row: companyComputePageVO) => {
  try {
    const res = await infoCompanyApi({ id: _row.id, logId: _row.lastLogId });
    Object.assign(resultForm, res);
    modalApi.open();
  } catch {
    message.error($t('base.resError'));
  }
};

const editSuccess = async (data: companyComputeVo) => {
  try {
    if (data.status === 'EFFECTIVE') {
      await againCompanyApi(data);
    } else {
      data.id ? await editCompanyApi(data) : await addCompanyApi(data);
    }
    if (addForm.value.companyType === 'operation_customer') {
      openFormPopup(false, {});
    } else {
      openMarketFormPopup(false, {});
    }
    message.success($t('base.resSuccess'));
    await gridApi.formApi.submitForm(); // 刷新列表数据
  } catch {
    message.error($t('base.resError'));
  }
};

const detail = async (_row: companyComputePageVO) => {
  try {
    const res = await infoCompanyApi({ id: _row.id, logId: _row.lastLogId });
    const data = cloneDeep(res);
    if (_row.companyType === 'region_customer') {
      openDetailFormPopup(true, data);
    } else if (_row.companyType === 'operation_customer') {
      openDetailOperationFormPopup(true, data);
    } else {
      openDetailMarketFormPopup(true, data);
    }
  } catch {}
};

const del = async (_row: companyComputePageVO) => {
  try {
    AModal.confirm({
      title: '确认删除',
      content: '确认删除此数据？',
      onOk: async () => {
        await deleteCompanyApi(_row.id);
        message.success($t('base.resSuccess'));
        await gridApi.reload();
      },
    });
  } catch {
    message.error('删除失败');
  }
};

const handleCompanyChange = (value: any) => {
  addForm.value.companyInfo = value.option;
  addForm.value.companyCode = value.option.value;
  addForm.value.companyName = value.option.label;
  addForm.value.cityName = value.option.city;
  addForm.value.districtName = value.option.district;
  addForm.value.districtCode = value.option.districtCode;
};

// 根据接口必填字段定义验证规则
const addRules: Record<string, Rule[]> = {
  companyInfo: [{ required: true, message: '请选择企业', trigger: 'change' }],
  companyType: [{ required: true, message: '请选择客户类别', trigger: 'change' }],
};
const rules: Record<string, Rule[]> = {
  creditAmount: [{ required: true, message: '请输入最后授信额度测算结果' }],
  adjustSupportingFileId: [{ required: true, message: '请选择上传调整佐证材料', trigger: 'change' }],
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="selectCompany()">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="ratChange(row)" v-if="row.companyType !== 'region_customer'">
            测算结果调整
          </TypographyLink>
          <TypographyLink v-if="row.status === 'SUBMIT' && row.companyType !== 'region_customer'" @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink v-if="row.status === 'SUBMIT'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)"> {{ $t('base.detail') }}</TypographyLink>
          <TypographyLink @click="detail(row)"> 审批</TypographyLink>
          <TypographyLink v-if="row.status === 'EFFECTIVE' && row.companyType !== 'region_customer'" @click="edit(row)">
            重新测算
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Edit @register="registerForm" @ok="editSuccess" />
    <MarketEdit @register="marketForm" @ok="editSuccess" />
    <Detail @register="detailForm" />
    <DetailOperation @register="detailOperationForm" />
    <DetailMarket @register="detailMarketForm" />
    <Modal title="测算结果调整" class="w-[800px]">
      <Form ref="resultFormRef" :model="resultForm" :label-col="labelCol" :rules="rules" :wrapper-col="{ span: 20 }">
        <FormItem label="企业名称" name="companyRating">
          <Input disabled v-model:value="resultForm.companyName" />
        </FormItem>
        <FormItem label="地级市">
          <Input disabled v-model:value="resultForm.cityName" />
        </FormItem>
        <FormItem label="区县">
          <Input disabled v-model:value="resultForm.districtName" />
        </FormItem>
        <FormItem label="客户类别">
          <Select
            disabled
            v-model:value="resultForm.companyType"
            :options="dictStore.getDictList('FCT_COMPANY_LIMIT_COMPANY_TYPE')"
          />
        </FormItem>
        <FormItem label="最后授信额度测算结果(元)" name="creditAmount">
          <Input v-model:value="resultForm.creditAmount" />
        </FormItem>
        <FormItem label="上传调整佐证材料" name="adjustSupportingFileId">
          <BaseFilePickList v-model="resultForm.adjustSupportingFileId" />
        </FormItem>
      </Form>
    </Modal>
    <AddModal title="测算结果调整" class="w-[800px]">
      <Form ref="addFormRef" :model="addForm" :label-col="labelCol" :rules="addRules" :wrapper-col="{ span: 20 }">
        <FormItem label="企业名称" name="companyInfo">
          <ApiComponent
            v-model="addForm.companyInfo"
            :component="Select"
            :api="getCompanyListApi"
            label-in-value="true"
            label-field="companyName"
            @change="handleCompanyChange"
            value-field="companyCode"
            model-prop-name="value"
          />
        </FormItem>
        <FormItem label="地级市">
          <Input disabled v-model:value="addForm.cityName" />
        </FormItem>
        <FormItem label="区县">
          <Input disabled v-model:value="addForm.districtName" />
        </FormItem>
        <FormItem label="客户类别" name="companyType">
          <Select
            v-model:value="addForm.companyType"
            :options="dictStore.getDictList('FCT_COMPANY_LIMIT_COMPANY_TYPE')"
          />
        </FormItem>
      </Form>
    </AddModal>
  </Page>
</template>

<style></style>
