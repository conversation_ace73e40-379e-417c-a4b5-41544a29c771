<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AbsProjectInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delAbsProjectApi, getAbsProjectPageListApi } from '#/api';

import ProjectEdit from './project-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: 'ABS项目名称',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: 'ABS项目编号',
    },
    {
      component: 'Input',
      fieldName: 'originalBeneficiaryName',
      label: '原始受益人',
    },
    {
      component: 'Input',
      fieldName: 'planManager',
      label: '计划管理人',
    },
    {
      component: 'Input',
      fieldName: 'guaranteeName',
      label: '担保人',
    },
    {
      component: 'Select',
      fieldName: 'statusList',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatusList',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'projectName', title: 'ABS项目名称', minWidth: 200 },
    { field: 'projectCode', title: 'ABS项目编号', minWidth: 200 },
    { field: 'originalBeneficiaryName', title: '原始受益人', minWidth: 120 },
    { field: 'planManager', title: '计划管理人', minWidth: 120 },
    { field: 'guaranteeName', title: '担保人', minWidth: 120 },
    { field: 'financeTermYear', title: '融资/发行期限（年）', minWidth: 200 },
    { field: 'declareFinancingScale', title: '融资规模（元）' },
    {
      field: 'productLayer',
      title: '产品分层',
      minWidth: 120,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_ABS_PROJECT_PRODUCT_LAYER' } },
    },
    { field: 'declarationPlace', title: '申报场所', minWidth: 200 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_STATUS' } },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: { name: 'CellStatus', props: { code: 'REVIEW_STATUS' } },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAbsProjectPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: AccessInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: AbsProjectInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该ABS项目，是否继续？',
    async onOk() {
      await delAbsProjectApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <ProjectEdit @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
