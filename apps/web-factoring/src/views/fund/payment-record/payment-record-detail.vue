<script setup lang="ts">
import type { PaymentRecordInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { omit } from 'lodash-es';

import { BaseFileList } from '#/adapter/base-ui';
import { getBusinessFileListApi, getPaymentRecordInfoApi } from '#/api';
import LadderPenaltyDetail from '#/views/project/components/ladder-penalty-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';

defineEmits(['register']);
const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: PaymentRecordInfo) => {
  let info = data.id ? await getPaymentRecordInfoApi(data.id as number) : data;
  const calculation = omit(info.calculation, 'id', 'confirmInvestAmount');
  info = {
    ...info,
    ...calculation,
  };
  const fileList = await getBusinessFileListApi({ businessId: info.id, businessType: 'FCT_PAYMENT_CONFIRM_VOUCHER' });
  info.fileId = fileList[0]?.id;
  recordForm.value = { ...recordForm.value, ...info };
};
const recordForm = ref<PaymentRecordInfo>({});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="付款记录详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="付款记录编号">
          {{ recordForm.confirmCode }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ recordForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位">
          {{ recordForm.payeeCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="付款单位">
          {{ recordForm.payerCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位开户行">
          {{ recordForm.payeeBankBranch }}
        </a-descriptions-item>
        <a-descriptions-item label="付款单位开户行">
          {{ recordForm.payerBankBranch }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位银行账号">
          {{ recordForm.payeeBankAccount }}
        </a-descriptions-item>
        <a-descriptions-item label="付款单位银行账号">
          {{ recordForm.payerBankAccount }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="确认付款信息" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="申请投放金额（元）">
          {{ recordForm.investAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="确认投放金额（元）">
          {{ recordForm.confirmInvestAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="计划投放日期">
          {{ recordForm.expectInvestDate }}
        </a-descriptions-item>
        <a-descriptions-item label="确认投放日期">
          {{ recordForm.confirmInvestDate }}
        </a-descriptions-item>
        <a-descriptions-item label="实际付款方式">
          {{ dictStore.formatter(recordForm.confirmPaymentMethod, 'FCT_PAYMENT_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="上传付款凭证">
          <BaseFileList :model-value="recordForm.fileId" />
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="3">
          {{ recordForm.remarks }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="回款测算" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="合同利率(%/年)">
          {{ recordForm.nominalInterestRate }}
        </a-descriptions-item>
        <a-descriptions-item label="定价综合收益率(%/年)">
          {{ recordForm.pricingXirrRate }}
        </a-descriptions-item>
        <a-descriptions-item label="服务费金额(元)">
          {{ recordForm.serviceFeeAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="宽限期天数(日)">
          {{ recordForm.gracePeriodDays }}
        </a-descriptions-item>
        <a-descriptions-item label="宽限期费率(%/年)">
          {{ recordForm.gracePeriodRate }}
        </a-descriptions-item>
        <a-descriptions-item label="逾期罚息设置">
          {{ dictStore.formatter(recordForm.penaltyType, 'FCT_PENALTY_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item v-if="recordForm.penaltyType === 'fixed'" label="固定罚息利率（%）">
          {{ recordForm.penaltyInterestRate }}
        </a-descriptions-item>
      </a-descriptions>
      <LadderPenaltyDetail v-show="recordForm.penaltyType === 'ladder'" :penalty-form="recordForm" />
      <RepaymentCalculationDetail
        :calculation-form="recordForm"
        :descriptions-prop="descriptionsProp"
        calculation-type="PaymentRecord"
      />
    </div>
  </BasicPopup>
</template>

<style></style>
