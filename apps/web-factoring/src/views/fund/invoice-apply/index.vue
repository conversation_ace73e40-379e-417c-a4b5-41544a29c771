<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InvoiceApplyInfo } from '#/api';

import { ref } from 'vue';

import { FilePreviewDialog } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  delInvoiceApplyApi,
  editInvoiceUploadApi,
  getBusinessFileListApi,
  getDownloadFileLinkApi,
  getInvoiceApplyPageListApi,
  getPreviewFileExternalLink,
  invoiceSingleOcrApi,
} from '#/api';

import InvoiceApplyDetail from './invoice-apply-detail.vue';
import InvoiceApplyEdit from './invoice-apply-edit.vue';

const dictStore = useDictStore();
// 搜索表单配置
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'projectName', label: '项目名称' },
    {
      component: 'Select',
      fieldName: 'invoiceItem',
      label: '开票内容',
      componentProps: {
        options: dictStore.getDictList('FCT_INVOICE_ITME'),
      },
    },
    {
      component: 'Select',
      fieldName: 'invoiceType',
      label: '发票类型',
      componentProps: {
        options: dictStore.getDictList('FCT_INVOICE_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'invoiceStatus',
      label: '开票状态',
      componentProps: {
        options: dictStore.getDictList('FCT_INVOICE_OPEN_STATUS'),
      },
    },
  ],
  commonConfig: { labelCol: { span: 6 }, wrapperCol: { span: 18 } },
});

// 表格列配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'invoiceType',
      title: '发票类型',
      minWidth: 150,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_TYPE' } },
    },
    { field: 'buyerName', title: '购买方名称', minWidth: 200 },
    { field: 'taxNumber', title: '税号', minWidth: 180 },
    { field: 'applicationDate', title: '开票申请日期', minWidth: 160 },
    { field: 'invoiceDate', title: '开票日期', minWidth: 160 },
    { field: 'invoiceNumber', title: '发票号码', minWidth: 180 },
    { field: 'invoiceCode', title: '发票代码', minWidth: 180 },
    {
      field: 'invoiceItem',
      title: '开票内容',
      minWidth: 150,
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_ITME' } },
    },
    { field: 'amount', title: '不含税金额（元）', minWidth: 150 },
    { field: 'tax', title: '税额（元）', minWidth: 150 },
    { field: 'amountTax', title: '价税合计（元）', minWidth: 150 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_STATUS' } },
      minWidth: 120,
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: { name: 'CellStatus', props: { code: 'REVIEW_STATUS' } },
      minWidth: 120,
    },
    {
      field: 'invoiceStatus',
      title: '开票状态',
      cellRender: { name: 'CellStatus', props: { code: 'FCT_INVOICE_OPEN_STATUS' } },
      minWidth: 120,
    },
    { field: 'action', title: '操作', fixed: 'right', width: 120, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getInvoiceApplyPageListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();

// 新增
const add = () => openFormPopup(true, {});
// 编辑
const edit = (row: InvoiceApplyInfo) => openFormPopup(true, row);
const editSuccess = () => gridApi.reload();
// 查看详情
const viewDetail = (row: InvoiceApplyInfo) => openDetailPopup(true, row);
// 删除
const del = () => {
  const selected = gridApi.grid.getCheckboxRecords(true);
  if (selected.length === 0) return message.error('请选择数据');
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该开票申请，是否继续？',
    async onOk() {
      await delInvoiceApplyApi(selected[0].id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const uploadInfo = (row: any) => {
  uploadForm.value.id = row.id;
  modalApi.open();
};
const rules = {
  invoiceDate: [{ required: true, message: '请选择开票日期', trigger: 'change' }],
  invoiceNumber: [{ required: true, message: '请输入发票号' }],
};
const pickFile = async (data: any) => {
  uploadForm.value.attachmentList = [data.id];
  uploadForm.value.businessType = 'FCT_INVOICE_UPLOAD';
  loading.value = true;
  try {
    const res: any = await invoiceSingleOcrApi({ fileId: data.id });
    loading.value = false;
    let { invoiceCode, invoiceNumber, billingDate: invoiceDate } = res;
    invoiceDate = dayjs(invoiceDate, 'YYYYMMDD').valueOf();
    uploadForm.value = { ...uploadForm.value, invoiceCode, invoiceNumber, invoiceDate };
  } catch {
    message.error('请上传正确发票');
    loading.value = false;
  }
};
const UploadFormRef = ref();
const loading = ref(false);
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await UploadFormRef.value.validate();
    await editInvoiceUploadApi(uploadForm.value);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
  onClosed: () => {
    uploadForm.value = {};
  },
});
const uploadForm = ref({});
const FilePreviewDialogRef = ref();
const imgTypeList = new Set(['bmp', 'gif', 'jpeg', 'jpg', 'png']);
const wordTypeList = ['doc', 'docx'];
const excelTypeList = ['xls', 'xlsx'];
const pptTypeList = ['ppt', 'pptx'];
const pdfTypeList = ['pdf'];
const previewTypeList = new Set([...excelTypeList, ...imgTypeList, ...pdfTypeList, ...pptTypeList, ...wordTypeList]);
const checkFile = async (file: any, type: string) => {
  const fileList = await getBusinessFileListApi({ businessId: file.id, businessType: 'FCT_INVOICE_UPLOAD' });
  if (type === 'Preview') {
    if (fileList[0].extension && previewTypeList.has(fileList[0].extension)) {
      FilePreviewDialogRef.value.init(fileList[0].id);
    } else {
      console.warn('文件不支持预览');
    }
  } else {
    const url = await getDownloadFileLinkApi({ id: fileList[0].id });

    window.open(url);
  }
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add">{{ $t('base.add') }}</a-button>
          <a-button type="primary" danger @click="del">{{ $t('base.del') }}</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">{{ $t('base.detail') }}</a-typography-link>
          <Dropdown v-if="['EFFECTIVE'].includes(row.status)">
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="application" @click="uploadInfo(row)" v-if="['invoicing'].includes(row.invoiceStatus)">
                  上传开票申请
                </MenuItem>
                <MenuItem
                  key="preview"
                  @click="checkFile(row, 'Preview')"
                  v-if="['invoiced'].includes(row.invoiceStatus)"
                >
                  预览
                </MenuItem>
                <MenuItem
                  key="download"
                  @click="checkFile(row, 'Download')"
                  v-if="['invoiced'].includes(row.invoiceStatus)"
                >
                  下载
                </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </a-space>
      </template>
    </Grid>
    <InvoiceApplyEdit @register="registerForm" @ok="editSuccess" />
    <InvoiceApplyDetail @register="registerDetail" />
    <Modal title="上传发票" class="w-[800px]">
      <a-form ref="UploadFormRef" :model="uploadForm" :rules="rules" v-loading="loading">
        <a-form-item label="上传发票" name="fileId">
          <BaseFilePickList v-model="uploadForm.fileId" @pick="pickFile" />
        </a-form-item>
        <a-form-item label="开票申请日期" name="invoiceDate">
          <a-date-picker v-model:value="uploadForm.invoiceDate" value-format="x" class="w-full" />
        </a-form-item>
        <a-form-item label="发票号码" name="invoiceNumber">
          <a-input v-model:value="uploadForm.invoiceNumber" />
        </a-form-item>
        <a-form-item label="发票代码" name="invoiceCode">
          <a-input v-model:value="uploadForm.invoiceCode" />
        </a-form-item>
      </a-form>
    </Modal>
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="getPreviewFileExternalLink" />
  </Page>
</template>
