<script setup lang="ts">
import type { PledgeVO } from '#/api';

import { reactive } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep } from '@vben/utils';

const baseFormInfo = reactive<PledgeVO>({
  id: undefined,
  mainContractAmount: undefined,
  mainContractCode: '',
  pledgeAmount: undefined,
  pledgeCode: '',
  pledgeContractCode: '',
  pledgeeCompanyCode: '',
  pledgeeCompanyName: '',
  pledgeName: '',
  pledgorCompanyCode: '',
  pledgorCompanyName: '',
  projectIdList: [],
  projectName: '',
  startDebtPeriodDate: undefined,
  endDebtPeriodDate: undefined,
  status: '',
  zdDueDate: undefined,
  isSubmit: false,
});

// 添加初始化方法
const init = (data: PledgeVO) => {
  if (data) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
  }
};

const [registerPopup] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="质押物名称">
          {{ baseFormInfo.pledgeName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="质押物编号">
          {{ baseFormInfo.pledgeCode || '-' }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="关联项目">
                    {{ baseFormInfo.projectName || '-' }}
                </a-descriptions-item> -->
        <a-descriptions-item label="质权人">
          {{ baseFormInfo.pledgeeCompanyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="出质人">
          {{ baseFormInfo.pledgorCompanyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="质押主合同号码">
          {{ baseFormInfo.mainContractCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="质押合同编号">
          {{ baseFormInfo.pledgeContractCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="主合同金额（元）">
          {{ baseFormInfo.mainContractAmount || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="质押财产价值（元）">
          {{ baseFormInfo.pledgeAmount || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="债务履行期限">
          {{
            baseFormInfo.startDebtPeriodDate && baseFormInfo.endDebtPeriodDate
              ? `${baseFormInfo.startDebtPeriodDate} 至 ${baseFormInfo.endDebtPeriodDate}`
              : '-'
          }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicPopup>
</template>

<style scoped></style>
