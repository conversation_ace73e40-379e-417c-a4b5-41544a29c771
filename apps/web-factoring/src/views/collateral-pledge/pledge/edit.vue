<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { PledgeVO } from '#/api';

import { computed, reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep } from '@vben/utils';

import { Button, Col, Form, FormItem, Input, RangePicker, Row, Select } from 'ant-design-vue';

import { getCompanyListApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

const baseFormInfo = reactive<PledgeVO>({
  id: undefined,
  mainContractAmount: undefined,
  mainContractCode: '',
  pledgeAmount: undefined,
  pledgeCode: '',
  pledgeContractCode: '',
  pledgeeCompanyCode: '',
  pledgeeCompanyName: '',
  pledgeName: '',
  pledgorCompanyCode: '',
  pledgorCompanyName: '',
  projectIdList: [],
  projectName: '',
  startDebtPeriodDate: undefined,
  endDebtPeriodDate: undefined,
  status: '',
  zdDueDate: undefined,
  isSubmit: false,
});
const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  pledgeName: [{ required: true, message: '请输入质押物名称' }],
  pledgorCompanyCode: [{ required: true, message: '请选择出质人', trigger: 'change' }],
  pledgeeCompanyCode: [{ required: true, message: '请选择质权人', trigger: 'change' }],
  mainContractAmount: [{ required: true, message: '请输入主合同金额' }],
  startDebtPeriodDate: [{ required: true, message: '请选择债务履行期限', trigger: 'change' }],
  pledgeAmount: [{ required: true, message: '请输入质押财产价值' }],
};
const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});

const formRef = ref();
const handleSave = async (isSubmit = false) => {
  try {
    await formRef.value.validate();
    const params = cloneDeep(baseFormInfo);
    // 如果是提交操作，设置isSubmit为true
    if (isSubmit) {
      params.isSubmit = true;
    }
    emit('ok', params);
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};

// 添加初始化方法
const init = (data: PledgeVO) => {
  if (data && Object.keys(data).length > 0) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
  } else {
    Object.assign(baseFormInfo, {
      endDebtPeriodDate: undefined,
      id: undefined,
      mainContractAmount: undefined,
      mainContractCode: '',
      pledgeAmount: undefined,
      pledgeCode: '',
      pledgeContractCode: '',
      pledgeeCompanyCode: '',
      pledgeeCompanyName: '',
      pledgeName: '',
      pledgorCompanyCode: '',
      pledgorCompanyName: '',
      projectIdList: [],
      projectName: '',
      startDebtPeriodDate: undefined,
      status: '',
      zdDueDate: undefined,
      isSubmit: false,
    });
  }
};

const pledgorChange = (value: any) => {
  baseFormInfo.pledgorCompanyName = value.label;
  baseFormInfo.pledgorCompanyCode = value.key;
};
const pledgeeChange = (value: any) => {
  baseFormInfo.pledgeeCompanyName = value.label;
  baseFormInfo.pledgeeCompanyCode = value.key;
};

// 添加计算属性 debtPeriodDate
const debtPeriodDate = computed({
  get: () => {
    return [baseFormInfo.startDebtPeriodDate, baseFormInfo.endDebtPeriodDate];
  },
  set: (value) => {
    baseFormInfo.startDebtPeriodDate = value[0];
    baseFormInfo.endDebtPeriodDate = value[1];
  },
});

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="baseFormInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="质押物名称" name="pledgeName">
            <Input v-model:value="baseFormInfo.pledgeName" placeholder="请输入质押物名称" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="质押物编号" name="pledgeCode">
            <Input disabled v-model:value="baseFormInfo.pledgeCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="出质人" name="pledgorCompanyCode">
            <ApiComponent
              v-model="baseFormInfo.pledgorCompanyCode as unknown as string"
              :component="Select"
              :api="getCompanyListApi"
              label-field="companyName"
              value-field="companyCode"
              model-prop-name="value"
              label-in-value="true"
              @change="pledgorChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="质权人" name="pledgeeCompanyCode">
            <ApiComponent
              v-model="baseFormInfo.pledgeeCompanyCode as unknown as string"
              :component="Select"
              :api="getCompanyListApi"
              label-field="companyName"
              value-field="companyCode"
              model-prop-name="value"
              label-in-value="true"
              @change="pledgeeChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="质押主合同号码" name="mainContractCode">
            <Input v-model:value="baseFormInfo.mainContractCode" placeholder="请输入质押主合同号码" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="质押合同编号" name="pledgeContractCode">
            <Input v-model:value="baseFormInfo.pledgeContractCode" placeholder="请输入质押合同编号" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="主合同金额（元）" name="mainContractAmount">
            <Input v-model:value="baseFormInfo.mainContractAmount" placeholder="请输入主合同金额（元）" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债务履行期限" name="startDebtPeriodDate">
            <RangePicker style="width: 100%" v-model:value="debtPeriodDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="质押财产价值（元）" name="pledgeAmount">
            <Input v-model:value="baseFormInfo.pledgeAmount" placeholder="请输入质押财产价值（元）" />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
