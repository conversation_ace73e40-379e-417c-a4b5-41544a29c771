<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PledgePageVO, PledgeVO } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Button, message, Modal, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addPledgeApi, delPledgeApi, editPledgeApi, getPledgePageListApi, infoPledgeApi } from '#/api';

import Detail from './detail.vue';
import Edit from './edit.vue';

const dictStore = useDictStore();

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'pledgeName',
      label: '质押物名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '关联项目',
    },
    {
      component: 'Input',
      fieldName: 'pledgorCompanyName',
      label: '出质人',
    },
    {
      component: 'Input',
      fieldName: 'pledgeeCompanyName',
      label: '质权人',
    },
    {
      component: 'Select',
      fieldName: 'statusList',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_NOT_REVIEW_STATUS'),
      },
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'pledgeName', title: '质押物名称', width: 280 },
    { field: 'projectName', title: '关联项目', width: 280 },
    { field: 'pledgorCompanyName', title: '出质人' },
    { field: 'pledgeeCompanyName', title: '质权人' },
    { field: 'mainContractCode', title: '质押主合同号码' },
    { field: 'pledgeContractCode', title: '质押合同编号' },
    { field: 'mainContractAmount', title: '主合同金额（元）' },
    { field: 'mortgageAmount', title: '债务履行期限', slots: { default: 'mortgageAmount-span' } },
    { field: 'pledgeAmount', title: '质押财产价值 （元）' },
    { field: 'status', title: '操作状态', formatter: ['formatStatus', 'FCT_NOT_REVIEW_STATUS'] },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPledgePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 打开新增页面
const add = () => {
  openFormPopup(true, {});
};

const edit = async (rowData: PledgePageVO) => {
  try {
    const res = await infoPledgeApi(rowData.id);
    const data = cloneDeep(res);
    openFormPopup(true, data);
  } catch {
    message.error($t('base.resError'));
  }
};

const editSuccess = async (data: PledgeVO) => {
  try {
    data.id ? await editPledgeApi(data) : await addPledgeApi(data);
    message.success($t('base.resSuccess'));
    openFormPopup(false, {});
    await gridApi.formApi.submitForm(); // 刷新列表数据
  } catch {
    message.error($t('base.resError'));
  }
};
const detail = async (_row: PledgePageVO) => {
  try {
    const res = await infoPledgeApi(_row.id);
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {
    // message.error('删除失败: ' + error.message);
  }
};

const del = async (rowData: PledgePageVO) => {
  try {
    Modal.confirm({
      title: $t('base.del'),
      content: $t('base.confirmDelContent'),
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await delPledgeApi(rowData.id);
          message.success($t('base.resSuccess'));
          await gridApi.formApi.submitForm(); // 刷新列表数据
        } catch {
          message.error('删除失败');
        }
      },
    });
  } catch {
    message.error('删除失败');
  }
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #mortgageAmount-span="{ row }"> {{ row.startDebtPeriodDate }} - {{ row.endDebtPeriodDate }} </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="row.status === 'SUBMIT'" @click="edit(row)"> {{ $t('base.edit') }} </TypographyLink>
          <TypographyLink @click="detail(row)"> {{ $t('base.detail') }} </TypographyLink>
          <TypographyLink v-if="row.status === 'SUBMIT'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Edit @register="registerForm" @ok="editSuccess" />
    <Detail @register="detailForm" />
  </Page>
</template>

<style></style>
