<script setup lang="ts">
import type { CollateralVO } from '#/api';

import { reactive } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

const dictStore = useDictStore();
const baseFormInfo = reactive<CollateralVO>({
  assessedAmount: undefined,
  id: undefined,
  issuingDate: undefined,
  mortgageAmount: undefined,
  mortgageCode: '',
  mortgageCompanyCode: '',
  mortgageCompanyName: '',
  mortgageeCompanyCode: '',
  mortgageeCompanyName: '',
  mortgageName: '',
  mortgageTerm: '',
  mortgageType: '',
  projectIdList: [],
  projectName: '',
  propertyLocation: '',
  rightsCertNo: '',
  status: '',
  uploadBy: undefined,
  uploadDate: undefined,
  uploadFileId: '',
  isSubmit: false,
});

// 添加初始化方法
const init = (data: CollateralVO) => {
  if (data) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
  }
};

const [registerPopup] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="抵押物名称">
          {{ baseFormInfo.mortgageName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="抵押物编号">
          {{ baseFormInfo.mortgageCode || '-' }}
        </a-descriptions-item>
        <!-- <a-descriptions-item label="关联项目">
                    {{ baseFormInfo.projectName || '-' }}
                </a-descriptions-item> -->
        <a-descriptions-item label="抵押权人">
          {{ baseFormInfo.mortgageeCompanyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="抵押人">
          {{ baseFormInfo.mortgageCompanyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="抵押物（预）评估价值（元）">
          {{ baseFormInfo.assessedAmount || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="抵押方式">
          {{ dictStore.formatter(baseFormInfo.mortgageType, 'MORTGAGE_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="抵押价值/最高债权数额（元）">
          {{ baseFormInfo.mortgageAmount || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="不动产坐落">
          {{ baseFormInfo.propertyLocation || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="抵押期限">
          {{ baseFormInfo.mortgageTerm || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicPopup>
</template>

<style scoped></style>
