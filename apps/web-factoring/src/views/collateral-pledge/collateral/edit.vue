<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { CollateralVO } from '#/api';

import { computed, reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { Button, Col, Form, FormItem, Input, Row, Select } from 'ant-design-vue';

import { getCompanyListApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

const dictStore = useDictStore();
const baseFormInfo = reactive<CollateralVO>({
  assessedAmount: undefined,
  id: undefined,
  issuingDate: undefined,
  mortgageAmount: undefined,
  mortgageCode: '',
  mortgageCompanyCode: '',
  mortgageCompanyName: '',
  mortgageeCompanyCode: '',
  mortgageeCompanyName: '',
  mortgageName: '',
  mortgageTerm: '',
  mortgageType: '',
  projectIdList: [],
  projectName: '',
  propertyLocation: '',
  rightsCertNo: '',
  status: '',
  uploadBy: undefined,
  uploadDate: undefined,
  uploadFileId: '',
  isSubmit: false,
});
const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  mortgageName: [{ required: true, message: '请输入抵押物名称' }],
  mortgageeCompanyCode: [{ required: true, message: '请选择抵押权人', trigger: 'change' }],
  mortgageCompanyCode: [{ required: true, message: '请选择抵押人', trigger: 'change' }],
  assessedAmount: [{ required: true, message: '请输入抵押物（预）评估价值（元）' }],
  mortgageAmount: [{ required: true, message: '请输入抵押价值/最高债权数额（元）' }],
  propertyLocation: [{ required: true, message: '请输入不动产坐落' }],
};
const title = computed(() => {
  return baseFormInfo.id ? '编辑' : '新增';
});

const formRef = ref();
const handleSave = async (isSubmit = false) => {
  try {
    await formRef.value.validate();

    const params = cloneDeep(baseFormInfo);

    // 如果是提交操作，设置isSubmit为true
    if (isSubmit) {
      params.isSubmit = true;
    }

    emit('ok', params);
  } catch (error) {
    console.error(`${isSubmit ? '提交' : '保存'}失败:`, error);
  } finally {
    changeOkLoading(false);
  }
};

const save = async () => {
  handleSave(false);
};

const submit = async () => {
  handleSave(true);
};

// 添加初始化方法
const init = (data: CollateralVO) => {
  if (data && Object.keys(data).length > 0) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
  } else {
    Object.assign(baseFormInfo, {
      assessedAmount: undefined,
      id: undefined,
      issuingDate: undefined,
      mortgageAmount: undefined,
      mortgageCode: '',
      mortgageCompanyCode: '',
      mortgageCompanyName: '',
      mortgageeCompanyCode: '',
      mortgageeCompanyName: '',
      mortgageName: '',
      mortgageTerm: '',
      mortgageType: '',
      projectIdList: [],
      projectName: '',
      propertyLocation: '',
      rightsCertNo: '',
      status: '',
      uploadBy: undefined,
      uploadDate: undefined,
      uploadFileId: '',
      isSubmit: false,
    });
  }
};

const mortgageChange = (value: any) => {
  baseFormInfo.mortgageCompanyName = value.label;
  baseFormInfo.mortgageCompanyCode = value.key;
};
const mortgageeChange = (value: any) => {
  baseFormInfo.mortgageeCompanyName = value.label;
  baseFormInfo.mortgageeCompanyCode = value.key;
};

const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="baseFormInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="抵押物名称" name="mortgageName">
            <Input v-model:value="baseFormInfo.mortgageName" placeholder="请输入抵押物名称" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押物编号" name="mortgageCode">
            <Input disabled v-model:value="baseFormInfo.mortgageCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押权人" name="mortgageeCompanyCode">
            <ApiComponent
              v-model="baseFormInfo.mortgageeCompanyCode"
              :component="Select"
              :api="getCompanyListApi"
              label-field="companyName"
              value-field="companyCode"
              label-in-value="true"
              model-prop-name="value"
              @change="mortgageeChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押人" name="mortgageCompanyCode">
            <ApiComponent
              v-model="baseFormInfo.mortgageCompanyCode as unknown as string"
              :component="Select"
              :api="getCompanyListApi"
              label-field="companyName"
              value-field="companyCode"
              model-prop-name="value"
              label-in-value="true"
              @change="mortgageChange"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押物（预）评估价值（元）" name="assessedAmount">
            <Input v-model:value="baseFormInfo.assessedAmount" placeholder="请输入抵押物（预）评估价值（元）" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押方式" name="mortgageType">
            <Select
              v-model:value="baseFormInfo.mortgageType"
              :options="dictStore.getDictList('MORTGAGE_TYPE')"
              placeholder="请选择抵押方式"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押价值/最高债权数额（元）" name="mortgageAmount">
            <Input v-model:value="baseFormInfo.mortgageAmount" placeholder="请输入抵押价值/最高债权数额（元）" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="不动产坐落" name="propertyLocation">
            <Input v-model:value="baseFormInfo.propertyLocation" placeholder="请输入不动产坐落" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="抵押期限" name="mortgageTerm">
            <Input v-model:value="baseFormInfo.mortgageTerm" placeholder="请输入抵押期限" />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
