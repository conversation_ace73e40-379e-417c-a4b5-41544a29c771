<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AdjustVo, CompanyRatingPageVO, CompanyRatingScoreVO, CompanyRatingVO } from '#/api';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getCompanyRatingScore } from '#/api';

import { spanMethod } from '../limit-admin/components/tableSpanUtils';

const dictStore = useDictStore();
const baseFormInfo = reactive<CompanyRatingVO>({
  companyCode: '',
  companyLogList: [],
  companyName: '',
  companyOrgType: '',
  id: undefined,
  lastLogId: undefined,
  oldCompanyRating: '',
  companyScoreList: [],
});

const ratingTable = ref([
  {
    firstCategory: '客户属性',
    secondCategory: '股东属性',
    dictType: 'COMPANY_RATING_SHAREHOLDER',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户属性',
    secondCategory: '行业属性',
    dictType: 'COMPANY_RATING_INDUSTRY',
    value: '',
    remark: '',
  },
  {
    firstCategory: '价值贡献',
    secondCategory: '业务覆盖',
    dictType: 'COMPANY_RATING_BUSINESS_COVERAGE',
    value: '',
    remark: '',
  },
  {
    firstCategory: '价值贡献',
    secondCategory: '利润贡献',
    dictType: 'COMPANY_RATING_PROFIT_CONTRIBUTION',
    value: '',
    remark: '',
  },
  {
    firstCategory: '价值贡献',
    secondCategory: '资源推荐',
    dictType: 'COMPANY_RATING_RESOURCE',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '收入增长',
    dictType: 'COMPANY_RATING_REVENUE_GROWTH',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '盈利能力',
    dictType: 'COMPANY_RATING_PROFIT_ORIENTED',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '信用情况',
    dictType: 'COMPANY_RATING_CREDIT_SITUATION',
    value: '',
    remark: '',
  },
  {
    firstCategory: '客户经营',
    secondCategory: '纳税情况',
    dictType: 'COMPANY_RATING_TAX_SITUATION',
    value: '',
    remark: '',
  },
  {
    firstCategory: '合作基础',
    secondCategory: '合作年限',
    dictType: 'COMPANY_RATING_DURATION',
    value: '',
    remark: '',
  },
  {
    firstCategory: '合作基础',
    secondCategory: '合作配合度',
    dictType: 'COMPANY_RATING_COOPERATION',
    value: '',
    remark: '',
  },
]);

// 添加初始化方法
const init = (data: CompanyRatingPageVO) => {
  if (data) {
    // 如果有传入数据，则使用传入数据初始化表单
    Object.assign(baseFormInfo, cloneDeep(data));
    gridReasultApi.grid?.reloadData(baseFormInfo.companyLogList);
  }
};

const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'firstCategory',
      title: '一级维度',
      width: 120,
    },
    {
      field: 'secondCategory',
      title: '二级维度',
      width: 120,
    },
    {
      field: 'value',
      title: '评级赋分',
      slots: { default: 'contractType' },
    },
    {
      field: 'remark',
      title: '备注',
      slots: { default: 'remark' },
    },
  ],
  data: ratingTable.value,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'firstCategory'),
};

const gridReasultOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'companyRatingDate', title: '评级日期', formatter: 'formatDate' },
    { field: 'companyRatingScore', title: '评级分数' },
    { field: 'companyRating', title: '当前评级结果', formatter: ['formatStatus', 'COMPANY_RATING'] },
    { field: 'companyRatingMethod', title: '评级方式', formatter: ['formatStatus', 'COMPANY_RATING_METHOD'] },
    { field: 'oldCompanyRating', title: '原评级结果', formatter: ['formatStatus', 'COMPANY_RATING'] },
    { field: 'applyReason', title: '申请原因' },
    { field: 'operationBy', title: '操作人' },
    {
      field: 'adjustSupportingFileId',
      title: '调整佐证材料',
      slots: { default: 'file-down' },
    },
    {
      field: 'action',
      title: '操作',
      slots: { default: 'action' },
    },
  ],
  data: baseFormInfo.companyLogList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [registerPopup] = usePopupInner((data) => init(data));
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await modalApi.close();
    } catch {}
  },
});

const [LevelModal, modalLevelApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await modalLevelApi.close();
    } catch {}
  },
});

const getDetail = async (row: CompanyRatingScoreVO) => {
  try {
    modalApi.open();
    const res = await getCompanyRatingScore({ id: row.id, companyRatingId: baseFormInfo.id });
    ratingTable.value = ratingTable.value.map((value, index) => {
      return {
        ...value,
        value: res[index].value,
        remark: res[index].remark,
      };
    });

    gridApi.grid?.reloadData(ratingTable.value);
  } catch {}
};

const companyForm = ref<AdjustVo>({
  id: 0,
  oldCompanyRating: '',
  companyOrgType: '',
  companyRating: '',
  applyReason: '',
  adjustSupportingFileId: 0,
});
const getRatingLevel = async (row: AdjustVo) => {
  try {
    companyForm.value.oldCompanyRating = row.oldCompanyRating;
    companyForm.value.companyOrgType = row.companyOrgType;
    companyForm.value.companyRating = row.companyRating;
    companyForm.value.applyReason = row.applyReason;
    companyForm.value.adjustSupportingFileId = row.adjustSupportingFileId;
    modalLevelApi.open();
  } catch {}
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const [GridReasult, gridReasultApi] = useVbenVxeGrid({ gridOptions: gridReasultOptions });
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="企业名称">
          {{ baseFormInfo.companyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码">
          {{ baseFormInfo.companyCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="客户属性">
          {{ dictStore.formatter(baseFormInfo.companyOrgType, 'FCT_FACTORING_TYPE') }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="客户分级赋分结果" />
      <GridReasult>
        <!-- 下载 -->
        <template #file-down="{ row }">
          <Space>
            <TypographyLink v-if="row.companyRatingMethod === 'level'" type="primary" @click="downLoad(row)">
              下载
            </TypographyLink>
          </Space>
        </template>

        <!-- 操作 -->
        <template #action="{ row }">
          <TypographyLink v-if="row.companyRatingMethod === 'score'" @click="getDetail(row)">查看</TypographyLink>
          <TypographyLink v-if="row.companyRatingMethod === 'level'" @click="getRatingLevel(row)">查看</TypographyLink>
        </template>
      </GridReasult>
    </div>
    <Modal title="评分详情" class="w-[1200px]">
      <Grid>
        <template #contractType="{ row }">
          {{ dictStore.formatter(row.value, row.dictType) }}
        </template>
        <template #remark="{ row }">
          <div v-if="row.secondCategory === '资源推荐'">
            <typography-text>1.项目落地，且推荐需留痕，需客户管理部认定。</typography-text><br />
            <typography-text>2. 具体如码头仓储合作、供应集采资源、数据金融模型、运营科技赋能等</typography-text>
          </div>
          <div v-if="row.secondCategory === '收入增长' || row.secondCategory === '盈利能力'">
            <typography-text>1. 项目关联集团最近连续2年收入增速，GDP增速参考省GDP增速。</typography-text><br />
            <typography-text>2. 项目关联集团最近连续2年收入增速，GDP增速参考省GDP增速。</typography-text>
          </div>
          <div>
            <typography-text>{{ row.remark }}</typography-text>
          </div>
        </template>
      </Grid>
    </Modal>
    <LevelModal title="层级详情" class="w-[1200px]">
      <div :class="BASE_PAGE_CLASS_NAME">
        <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
          <a-descriptions-item label="企业名称">
            {{ baseFormInfo.companyName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="统一社会信用代码">
            {{ baseFormInfo.companyCode || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="客户属性">
            {{ dictStore.formatter(baseFormInfo.companyOrgType, 'FCT_FACTORING_TYPE') }}
          </a-descriptions-item>
          <a-descriptions-item label="客户分级原分层结果">
            {{ dictStore.formatter(companyForm.oldCompanyRating, 'COMPANY_RATING') }}
          </a-descriptions-item>
          <a-descriptions-item label="本次客户层级调整为">
            {{ dictStore.formatter(companyForm.companyRating, 'COMPANY_RATING') }}
          </a-descriptions-item>
          <a-descriptions-item label="申请调整原因">
            {{ companyForm.applyReason || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="上传调整佐证材料">
            <BaseFilePickList v-model="companyForm.adjustSupportingFileId" />
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </LevelModal>
  </BasicPopup>
</template>

<style scoped></style>
