<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AdjustVo, CompanyRatingPageVO, CompanyRatingVO } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { Form, FormItem, Input, message, Select, Space, Textarea, TypographyLink } from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  adjustCompanyRatingApi,
  editCompanyRatingApi,
  getCompanyRatingPageListApi,
  getCompanyRatingScore,
  infoCompanyRatingApi,
} from '#/api';

import Detail from './detail.vue';
import Edit from './edit.vue';

const dictStore = useDictStore();

const companyForm = ref<Partial<AdjustVo>>({});
const labelCol = { style: { width: '150px' } };

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'companyCode',
      label: '统一社会信用代码',
    },
    {
      component: 'Select',
      fieldName: 'companyRatingList',
      label: '企业级别',
      componentProps: {
        options: dictStore.getDictList('COMPANY_RATING'),
      },
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'companyName', title: '企业名称', width: 280 },
    { field: 'companyCode', title: '统一社会信用代码' },
    { field: 'companyRatingScore', title: '评级分数' },
    { field: 'companyRating', title: '企业级别', formatter: ['formatStatus', 'COMPANY_RATING'] },
    { field: 'operationBy', title: '操作人' },
    { field: 'companyRatingDate', title: '最新评级日期', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCompanyRatingPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [detailForm, { openPopup: openDetailFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 打开新增页面
const ratChange = async (rowData: CompanyRatingPageVO) => {
  try {
    const res = await infoCompanyRatingApi(rowData.id);
    companyForm.value = cloneDeep(res);
    modalApi.open();
  } catch {
    message.error($t('base.resError'));
  }
};

const editSuccess = async (data: CompanyRatingVO) => {
  try {
    await editCompanyRatingApi(data);
    message.success($t('base.resSuccess'));
    openFormPopup(false, {});
    await gridApi.formApi.submitForm(); // 刷新列表数据
  } catch {
    message.error($t('base.resError'));
  }
};

const detail = async (_row: CompanyRatingPageVO) => {
  try {
    const res = await infoCompanyRatingApi(_row.id);
    const data = cloneDeep(res);
    openDetailFormPopup(true, data);
  } catch {}
};

const againRat = async (rowData: CompanyRatingPageVO) => {
  try {
    const res = await infoCompanyRatingApi(rowData.id);
    let data;

    if (res.lastLogId) {
      const ratingRes = await getCompanyRatingScore({ id: res.lastLogId, companyRatingId: res.id });
      data = {
        ...res,
        companyScoreList: ratingRes || [],
      };
    } else {
      data = cloneDeep(res);
    }

    openFormPopup(true, data);
  } catch {
    message.error($t('base.resError'));
  }
};

const companyFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await companyFormRef.value.validate();
      await adjustCompanyRatingApi(companyForm.value as AdjustVo);
      await modalApi.close();
      await gridApi.reload();
    } catch {}
  },
});
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  companyRating: [{ required: true, message: '请选择本次客户层级调整', trigger: 'change' }],
  adjustSupportingFileId: [{ required: true, message: '请上传调整佐证材料', trigger: 'change' }],
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="ratChange(row)"> 客户层级调整</TypographyLink>
          <TypographyLink @click="detail(row)"> {{ $t('base.detail') }}</TypographyLink>
          <TypographyLink @click="againRat(row)">重新评级</TypographyLink>
        </Space>
      </template>
    </Grid>
    <Edit @register="registerForm" @ok="editSuccess" />
    <Detail @register="detailForm" />
    <Modal title="客户层级调整" class="w-[800px]">
      <Form ref="companyFormRef" :model="companyForm" :label-col="labelCol" :rules="rules" :wrapper-col="{ span: 20 }">
        <FormItem label="企业名称" name="companyName">
          <Input disabled v-model:value="companyForm.companyName" />
        </FormItem>
        <FormItem label="统一社会信用代码" name="companyCode">
          <Input disabled v-model:value="companyForm.companyCode" />
        </FormItem>
        <FormItem label="客户属性" name="companyOrgType">
          <Input disabled v-model:value="companyForm.companyOrgType" />
        </FormItem>
        <FormItem label="客户分级原分层结果" name="oldCompanyRating">
          <Select
            disabled
            v-model:value="companyForm.oldCompanyRating"
            :options="dictStore.getDictList('COMPANY_RATING')"
          />
        </FormItem>
        <FormItem label="本次客户层级调整为" name="companyRating">
          <Select v-model:value="companyForm.companyRating" :options="dictStore.getDictList('COMPANY_RATING')" />
        </FormItem>
        <FormItem label="申请调整原因" name="applyReason">
          <Textarea v-model:value="companyForm.applyReason" :rows="4" placeholder="请输入申请调整原因" />
        </FormItem>
        <FormItem label="上传调整佐证材料" name="adjustSupportingFileId">
          <BaseFilePickList v-model="companyForm.adjustSupportingFileId" />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
