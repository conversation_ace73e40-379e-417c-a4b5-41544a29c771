<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { SupervisePageVO, SuperviseParams } from '#/api';

import { reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  message,
  Row,
  Select,
  Space,
  TypographyLink,
} from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addSuperviseApi, addSuperviseFileApi, delSuperviseApi, getSupervisePageListApi } from '#/api';

const labelCol = { style: { width: '150px' } };

// 定义验证规则
const rules: Record<string, Rule[]> = {
  supervisionYear: [{ required: true, message: '请选择年份', trigger: 'change' }],
  supervisionQuarter: [{ required: true, message: '请选择季度', trigger: 'change' }],
  supervisionFileId: [{ required: true, message: '请上传报告', trigger: 'change' }],
  reportName: [{ required: true, message: '请输入运营监督报告名称' }],
};
// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '运营监督报告名称',
    },
    {
      component: 'Input',
      fieldName: 'businessDate', // 修复字段名：productName -> businessDate
      label: '监督时段',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

const quarterOptions = [
  { value: 'Q1', label: '第一季度' },
  { value: 'Q2', label: '第二季度' },
  { value: 'Q3', label: '第三季度' },
  { value: 'Q4', label: '第四季度' },
];

// 修复表格列显示和数据绑定的bug
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'reportName', title: '运营监督报告名称' },
    { field: 'businessStructure', title: '监督时段', slots: { default: 'businessStructure' } },
    { field: 'lastUploadBy', title: '上传人' },
    { field: 'lastUploadDate', title: '上传日期', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getSupervisePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const addForm = reactive<SuperviseParams>({
  id: undefined,
  reportName: '',
  supervisionFileId: undefined,
  supervisionQuarter: '',
  supervisionYear: '',
});

const addFormRef = ref();
// 打开模态框方法
const add = () => {
  Object.assign(addForm, {
    id: undefined,
    reportName: '',
    supervisionFileId: undefined,
    supervisionQuarter: '',
    supervisionYear: '',
  });
  modalApi.open();
};

const edit = (_row: SupervisePageVO) => {
  const { id, reportName, supervisionFileId, supervisionQuarter, supervisionYear } = _row;
  Object.assign(addForm, {
    id,
    reportName,
    supervisionFileId,
    supervisionQuarter,
    supervisionYear,
  });
  modalApi.open();
};

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await addFormRef.value.validate();
      await (addForm.id
        ? addSuperviseFileApi(addForm as SuperviseParams)
        : addSuperviseApi(addForm as SuperviseParams));
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } catch {
      message.error('操作失败');
    }
  },
});

const del = async (_row: SupervisePageVO) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      try {
        await delSuperviseApi(_row.id);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch {
        // message.error('删除失败: ' + error.message);
      }
    },
  });
};

const upLoadReport = () => {};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #businessStructure="{ row }"> {{ row.supervisionYear }}年{{ row.supervisionQuarter }} </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)"> 重新上传</TypographyLink>
          <TypographyLink @click="upLoadReport(row)"> 下载</TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal title="上传运营监督报告" class="w-[800px]">
      <Form
        ref="addFormRef"
        :label-col="labelCol"
        :wrapper-col="{ span: 30 }"
        :model="addForm"
        class="mt-5 px-5"
        :rules="rules"
      >
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="上传运营监督报告" name="supervisionFileId">
              <BaseFilePickList v-model="addForm.supervisionFileId" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="监督年份" name="supervisionYear">
              <DatePicker
                :disabled="addForm.id"
                style="width: 100%"
                v-model:value="addForm.supervisionYear"
                value-format="YYYY"
                picker="year"
              />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="监督季度" name="supervisionQuarter">
              <Select :disabled="addForm.id" v-model:value="addForm.supervisionQuarter" :options="quarterOptions" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="运营监督报告名称" name="reportName">
              <Input :disabled="addForm.id" v-model:value="addForm.reportName" placeholder="请输入运营监督报告名称" />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
