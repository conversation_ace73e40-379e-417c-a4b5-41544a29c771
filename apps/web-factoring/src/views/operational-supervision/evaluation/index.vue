<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { EvaluationPageVO, EvaluationVo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Select, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addEvaluationApi,
  addEvaluationFileApi,
  delEvaluationApi,
  getEvaluationPageListApi,
  getProjectListApi,
} from '#/api';

const labelCol = { style: { width: '150px' } };

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目后评价名称',
    },
    {
      component: 'Input',
      fieldName: 'businessDate', // 修复字段名：productName -> businessDate
      label: '结清日期',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

// 修复表格列显示和数据绑定的bug
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'reportName', title: '项目后评价名称' },
    { field: 'projectName', title: '项目名称' },
    { field: 'settleDate', title: '结清日期' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getEvaluationPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const edit = (_row: EvaluationPageVO) => {
  const { projectId, reportName, evaluationFileId, settleDate, id } = _row;
  Object.assign(addForm, {
    id,
    projectId,
    reportName,
    evaluationFileId,
    settleDate,
  });
  modalApi.open();
};
const addFormRef = ref();

const addForm = reactive<EvaluationVo>({
  evaluationFileId: undefined,
  projectId: undefined,
  reportName: '',
  settleDate: '',
  id: undefined,
});

const add = () => {
  Object.assign(addForm, {
    reportName: '',
    evaluationFileId: undefined,
    projectId: undefined,
    settleDate: '',
    id: undefined,
  });
  modalApi.open();
};

const del = async (_row: EvaluationPageVO) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      try {
        await delEvaluationApi(_row.id);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch {
        // message.error('删除失败: ' + error.message);
      }
    },
  });
};

// 定义验证规则
const rules: Record<string, Rule[]> = {
  settleDate: [{ required: true, message: '请选择结清日期', trigger: 'change' }],
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  evaluationFileId: [{ required: true, message: '请上传项目后评价报告', trigger: 'change' }],
  reportName: [{ required: true, message: '请输入项目后评价名称' }],
};

const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    try {
      await addFormRef.value.validate();
      await (addForm.id ? addEvaluationFileApi(addForm as EvaluationVo) : addEvaluationApi(addForm as EvaluationVo));
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } catch {
      message.error('操作失败');
    }
  },
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)"> 重新上传 </TypographyLink>
          <TypographyLink @click="upload(row)"> 下载 </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal title="上传项目后评价" class="w-[800px]">
      <Form
        ref="addFormRef"
        :label-col="labelCol"
        :wrapper-col="{ span: 30 }"
        :model="addForm"
        class="mt-5 px-5"
        :rules="rules"
      >
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="上传项目后评价报告" name="evaluationFileId">
              <BaseFilePickList v-model="addForm.evaluationFileId" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="关联项目" name="projectId">
              <ApiComponent
                :disabled="addForm.id"
                v-model="addForm.projectId as unknown as string"
                :component="Select"
                :api="getProjectListApi"
                :params="{ status: 'EFFECTIVE', isMeetingCompleted: 1 }"
                label-field="projectName"
                value-field="id"
                model-prop-name="value"
              />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="结清日期" name="settleDate">
              <DatePicker
                :disabled="addForm.id"
                style="width: 100%"
                v-model:value="addForm.settleDate"
                value-format="YYYY-MM-DD"
              />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="项目后评价名称" name="reportName">
              <Input :disabled="addForm.id" v-model:value="addForm.reportName" placeholder="请输入项目后评价名称" />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
