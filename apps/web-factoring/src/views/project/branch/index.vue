<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { BranchInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { branchMeetingUploadApi, delBranchApi, getBranchPageListApi } from '#/api';

import BranchDetail from './branch-detail.vue';
import BranchEdit from './branch-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList('FCT_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'businessUserNames',
      label: '业务经理',
    },
    {
      component: 'Input',
      fieldName: 'operationsUserNames',
      label: '运营经理',
    },
    {
      component: 'Input',
      fieldName: 'riskUserNames',
      label: '风控经理',
    },
    {
      component: 'Input',
      fieldName: 'financeUserNames',
      label: '财务经理',
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'targetCompanyName', title: '合作企业', minWidth: 200 },
    { field: 'businessUserNames', title: '业务经理' },
    { field: 'operationsUserNames', title: '运营经理' },
    { field: 'riskUserNames', title: '风控经理' },
    { field: 'financeUserNames', title: '财务经理' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getBranchPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: BranchInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: BranchInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: BranchInfo) => {
  openDetailPopup(true, row);
};
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该会议，是否继续？',
    async onOk() {
      await delBranchApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const uploadInfo = (row: any, type: string) => {
  uploadForm.value.type = type;
  uploadForm.value.id = row.id;
  uploadForm.value.label = {
    meeting: '上传支委会议纪要',
  }[type];
  uploadForm.value.businessType = {
    meeting: 'FCT_PROJECT_MEETING_PARTY_MINUTES',
  }[type];
  modalApi.open();
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    const api = {
      meeting: branchMeetingUploadApi,
    }[uploadForm.value.type];
    await api(uploadForm.value);
    message.success($t('base.resSuccess'));
    await modalApi.close();
  },
  onClosed: () => {
    uploadForm.value = {
      businessType: '',
    };
  },
});
const uploadForm = ref({
  businessType: '',
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <Dropdown v-if="['EFFECTIVE'].includes(row.status)">
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="application" @click="uploadInfo(row, 'meeting')"> 上传支委会议纪要 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </a-space>
      </template>
    </Grid>
    <BranchEdit @register="registerForm" @ok="editSuccess" />
    <BranchDetail @register="registerDetail" />
    <Modal :title="uploadForm.label" class="w-[800px]">
      <BaseAttachmentList
        v-model="uploadForm.attachmentList"
        :business-id="uploadForm.id"
        :business-type="uploadForm.businessType"
        edit-mode
      />
    </Modal>
  </Page>
</template>

<style></style>
