<script setup lang="ts">
import type { InitiationInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { editComprehensiveOverviewApi, editSingleOverviewApi, getOverviewInfoApi } from '#/api';
import ComprehensiveChange from '#/views/project/overview/components/comprehensive-change.vue';
import SingleChange from '#/views/project/overview/components/single-change.vue';

const emit = defineEmits(['ok', 'register']);

const initiationForm = ref<InitiationInfo>({});
const ComprehensiveFormRef = ref();
const SingleFormRef = ref();
const init = async (data: InitiationInfo) => {
  initiationForm.value = data?.id ? await getOverviewInfoApi(data.id as number) : data;
  initCompanyList();
  initUserList();
  if (initiationForm.value.projectType === 'single') {
    SingleFormRef.value.init(initiationForm.value);
  }
};
// 初始化关联企业数据
const initCompanyList = () => {
  const companyList = initiationForm.value.companyList || [];

  // 提取担保企业
  const guarantorCompany = companyList.find((item: object) => item.projectCompanyType === 'guarantee');
  initiationForm.value.guarantor = guarantorCompany?.companyCode || '';

  // 提取授信对象
  const creditCompany = companyList.find((item: object) => item.projectCompanyType === 'credit');
  initiationForm.value.credit = creditCompany?.companyCode || '';

  // 提取债权人
  initiationForm.value.creditor =
    companyList
      .filter((item: object) => item.projectCompanyType === 'creditor')
      .map((item: object) => item.companyCode || '') || [];
  // 提取债务人
  initiationForm.value.debtor =
    companyList
      .filter((item: object) => item.projectCompanyType === 'debtor')
      .map((item: object) => item.companyCode || '') || [];
  // 提取用信企业（支持多选）
  initiationForm.value.creditUser =
    companyList
      .filter((item: object) => item.projectCompanyType === 'using')
      .map((item: object) => item.companyCode || '') || [];
};
// 初始化相关人员数据
const initUserList = () => {
  const userList = initiationForm.value.userList || [];

  // 提取业务经理
  const businessUser = userList.find((item: object) => item.projectUserType === 'business');
  initiationForm.value.businessManager = businessUser?.userId;

  // 提取运营经理
  const operationsUser = userList.find((item: object) => item.projectUserType === 'operations');
  initiationForm.value.operationsManager = operationsUser?.userId;

  // 提取风控经理
  const riskUser = userList.find((item: object) => item.projectUserType === 'risk');
  initiationForm.value.riskControlManager = riskUser?.userId;

  // 提取财务经理
  const financeUser = userList.find((item: object) => item.projectUserType === 'finance');
  initiationForm.value.financialManager = financeUser?.userId;
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const loading = reactive({
  submit: false,
});
const save = async (type?: string) => {
  await (initiationForm.value.projectType === 'comprehensive'
    ? ComprehensiveFormRef.value.save()
    : SingleFormRef.value.save());

  const formData = cloneDeep(initiationForm.value);
  // 临时赋值
  formData.creditCalculateAmount =
    formData.projectType === 'comprehensive' ? formData.creditAmount : formData.financingAmount;
  if (type === 'submit') formData.isSubmit = true;
  loading.submit = true;
  let api;
  if (formData.id) {
    api = formData.projectType === 'comprehensive' ? editComprehensiveOverviewApi : editSingleOverviewApi;
  }
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目信息变更" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <ComprehensiveChange
        v-show="initiationForm.projectType === 'comprehensive'"
        ref="ComprehensiveFormRef"
        v-model="initiationForm"
      />
      <SingleChange v-show="initiationForm.projectType === 'single'" ref="SingleFormRef" v-model="initiationForm" />
    </div>
  </BasicPopup>
</template>

<style></style>
