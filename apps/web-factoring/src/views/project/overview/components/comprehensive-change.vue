<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { InitiationInfo, ProjectCompanyBO, ProjectUserBO } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { Select } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCompanyListApi, getUserListApi } from '#/api';

const dictStore = useDictStore();
const initiationForm = defineModel<InitiationInfo>({ type: Object, required: true });
const colSpan = COL_SPAN_PROP;
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
// 验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称' }],
  credit: [{ required: true, message: '请输入授信对象', trigger: 'change' }],
  // guarantor: [{ required: true, message: '请选择担保人', trigger: 'change' }],
  // creditCalculateAmount: [{ required: true, message: '请输入客户测算额度' }],
  creditAmount: [{ required: true, message: '请输入授信额度' }],
  creditSingleMaxUsed: [{ required: true, message: '请输入单笔用信额度上限' }],
  creditTerm: [{ required: true, message: '请输入授信期限' }],
  creditRate: [{ required: true, message: '请输入授信费率' }],
  creditMinPeriod: [{ required: true, message: '请输入最低用信期限' }],
  creditType: [{ required: true, message: '请选择是否循环额度', trigger: 'change' }],
  creditUser: [{ required: true, message: '请选择是用信主体', trigger: 'change' }],
};

// 保存数据时转换关联企业数据
const saveCompanyList = (): ProjectCompanyBO[] => {
  const companyList: ProjectCompanyBO[] = [];

  // 添加担保企业
  if (initiationForm.value.guarantor) {
    companyList.push({
      companyCode: initiationForm.value.guarantor,
      companyName: getCompanyLabel(initiationForm.value.guarantor),
      projectCompanyType: 'guarantee',
    });
  }

  // 添加授信对象
  if (initiationForm.value.credit) {
    companyList.push({
      companyCode: initiationForm.value.credit,
      companyName: getCompanyLabel(initiationForm.value.credit),
      projectCompanyType: 'credit',
    });
  }

  // 添加用信企业（支持多选）
  if (initiationForm.value.creditUser?.length > 0) {
    initiationForm.value.creditUser.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'using',
        });
      }
    });
  }

  return companyList;
};
// 公司列表
const companyOptions = ref<{ companyCode: string; companyName: string }[]>([]);

const loadCompanyOptions = async () => {
  companyOptions.value = await getCompanyListApi();
};

// 初始化时加载公司列表
loadCompanyOptions();

// 获取公司名称
const getCompanyLabel = (companyCode: string): string => {
  if (!companyCode) return '';

  // 从缓存中查找
  const company = companyOptions.value.find((item) => item.companyCode === companyCode);

  return company?.companyName || '';
};

const userOptions = ref<{ userId: number; userName: string }[]>([]);

const loadUserOptions = async () => {
  userOptions.value = await getUserListApi();
};

loadUserOptions();

const filterOption = (input: string, option: any) => {
  return option.realName.toLowerCase().includes(input.toLowerCase());
};

// 获取人员名称
const getUserLabel = (userId: string): string => {
  if (!userId) return '';

  // 从缓存中查找
  const user = userOptions.value.find((item) => item.id === userId);

  return user?.realName || '';
};

// 保存数据时转换相关人员数据
const saveUserList = (): ProjectUserBO[] => {
  const userList: ProjectUserBO[] = [];

  // 添加业务经理
  if (initiationForm.value.businessManager) {
    userList.push({
      userId: initiationForm.value.businessManager,
      userName: getUserLabel(initiationForm.value.businessManager),
      projectUserType: 'business',
    });
  }

  // 添加运营经理
  if (initiationForm.value.operationsManager) {
    userList.push({
      userId: initiationForm.value.operationsManager,
      userName: getUserLabel(initiationForm.value.operationsManager),
      projectUserType: 'operations',
    });
  }

  // 添加风控经理
  if (initiationForm.value.riskControlManager) {
    userList.push({
      userId: initiationForm.value.riskControlManager,
      userName: getUserLabel(initiationForm.value.riskControlManager),
      projectUserType: 'risk',
    });
  }

  // 添加财务经理
  if (initiationForm.value.financialManager) {
    userList.push({
      userId: initiationForm.value.financialManager,
      userName: getUserLabel(initiationForm.value.financialManager),
      projectUserType: 'finance',
    });
  }

  return userList;
};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();

  // 保存关联企业数据
  initiationForm.value.companyList = saveCompanyList();

  // 保存相关人员数据
  initiationForm.value.userList = saveUserList();

  return initiationForm.value;
};

defineExpose({ save });
</script>

<template>
  <a-form ref="formRef" :model="initiationForm" :rules="rules" v-bind="formProp" class="">
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="项目类型" name="projectType">
          <a-select
            v-model:value="initiationForm.projectType"
            :options="dictStore.getDictList('FCT_PROJECT_TYPE')"
            disabled
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="合作企业" name="targetCompanyCode">
          <ApiComponent
            v-model="initiationForm.targetCompanyCode as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            :disabled="true"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="地市" name="cityName">
          <a-input v-model:value="initiationForm.cityName" disabled />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="区县" name="districtName">
          <a-input v-model:value="initiationForm.districtName" disabled />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="项目名称" name="projectName">
          <a-input v-model:value="initiationForm.projectName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="项目编号" name="projectCode">
          <a-input v-model:value="initiationForm.projectCode" disabled />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 项目背景 -->
    <BasicCaption content="项目背景" />
    <a-row class="mt-5">
      <a-col :span="24">
        <a-form-item label="项目背景描述" name="backgroundDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.backgroundDesc" :rows="4" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 交易主体信息 -->
    <BasicCaption content="交易主体信息" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="授信对象" name="credit">
          <ApiComponent
            v-model="initiationForm.credit as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            disabled
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="担保人" name="guarantor">
          <ApiComponent
            v-model="initiationForm.guarantor as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="授信对象基本情况" name="creditRecipientDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditRecipientDesc" :rows="4" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="担保人基本情况" name="guarantorInfoDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.guarantorInfoDesc" :rows="4" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 授信方案 -->
    <BasicCaption content="授信方案" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="客户测算额度（元）" name="creditCalculateAmount">
          <a-input v-model:value="initiationForm.creditCalculateAmount" disabled />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="授信额度（元）" name="creditAmount">
          <a-input v-model:value="initiationForm.creditAmount" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="单笔用信额度上限（元）" name="creditSingleMaxUsed">
          <a-input v-model:value="initiationForm.creditSingleMaxUsed" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="授信期限（个月）" name="creditTerm">
          <a-input-number v-model:value="initiationForm.creditTerm" :controls="false" class="w-full" :precision="0" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="授信费率（%）" name="creditRate">
          <a-input v-model:value="initiationForm.creditRate" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="用信主体" name="creditUser">
          <ApiComponent
            v-model="initiationForm.creditUser as unknown as any"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            mode="multiple"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="最低用信期限（个月）" name="creditMinPeriod">
          <a-input-number
            v-model:value="initiationForm.creditMinPeriod"
            :controls="false"
            class="w-full"
            :precision="0"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="授信额度类型" name="creditType">
          <a-radio-group
            v-model:value="initiationForm.creditType"
            :options="dictStore.getDictList('FCT_CREDIT_TYPE')"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="增信措施" name="creditEnhancementDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditEnhancementDesc" :rows="4" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="资金用途" name="fundUsageDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.fundUsageDesc" :rows="4" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 项目负责人 -->
    <BasicCaption content="项目负责人" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="业务经理" name="businessManager">
          <a-select
            v-model:value="initiationForm.businessManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="风控经理" name="riskControlManager">
          <a-select
            v-model:value="initiationForm.riskControlManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="运营经理" name="operationsManager">
          <a-select
            v-model:value="initiationForm.operationsManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="财务经理" name="financialManager">
          <a-select
            v-model:value="initiationForm.financialManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <BasicCaption content="其他情况说明" />
    <a-row class="mt-5">
      <a-col :span="24">
        <a-form-item label="其他情况说明" name="creditOtherDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditOtherDesc" :rows="4" />
        </a-form-item>
      </a-col>
    </a-row>
    <BaseAttachmentList
      v-model="initiationForm.attachmentList"
      :business-id="initiationForm.id"
      business-type="FCT_PROJECT"
      edit-mode
    />
  </a-form>
</template>
