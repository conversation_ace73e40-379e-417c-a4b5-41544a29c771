<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { getOverviewLogApi, getOverviewLogListApi } from '#/api';
import ComprehensiveDetail from '#/views/project/initiation/components/comprehensive-detail.vue';
import SingleDetail from '#/views/project/initiation/components/single-detail.vue';

const gridOptions = {
  columns: [
    {
      field: 'changeDate',
      title: '项目变更时间',
      minWidth: '150px',
    },
    {
      field: 'projectName',
      title: '项目名称',
      minWidth: '150px',
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 100,
      slots: { default: 'action' },
    },
  ],
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }) => {
        return await getOverviewLogListApi({
          current: page.currentPage,
          size: page.pageSize,
          projectId: form.value.id,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
} as VxeTableGridOptions;
const [grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const form = ref({});
const init = (data: any) => {
  form.value = data;
  gridApi.reload();
};
const projectForm = ref({});
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  onClosed: () => {
    modalApi.close();
  },
});
const handleView = async (row: any = {}) => {
  projectForm.value = await getOverviewLogApi(row.id);
  projectForm.value.businessTypeFile = 'FCT_PROJECT';
  modalApi.open();
};
defineExpose({ init });
</script>

<template>
  <div>
    <grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="handleView(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </grid>
    <Modal title="历史版本信息" class="w-[1000px]">
      <ComprehensiveDetail v-show="projectForm.projectType === 'comprehensive'" :form="projectForm" />
      <SingleDetail v-show="projectForm.projectType === 'single'" :form="projectForm" />
    </Modal>
  </div>
</template>
