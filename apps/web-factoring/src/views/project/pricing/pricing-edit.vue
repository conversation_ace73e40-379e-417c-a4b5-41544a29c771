<script setup lang="ts">
import type { InitiationInfo, PricingInfo } from '#/api';

import { nextTick, reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep, omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addComprehensivePricingApi,
  addSinglePricingApi,
  editComprehensivePricingApi,
  editSinglePricingApi,
  getOverviewInfoApi,
  getPricingInfoApi,
  getProjectListApi,
} from '#/api';
import LadderPenalty from '#/views/project/components/ladder-penalty.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const RepaymentCalculationRef = ref();
const LadderPenaltyRef = ref();
const init = async (data: PricingInfo) => {
  if (data.id) {
    let info = data.id ? await getPricingInfoApi(data.id as number) : data;
    if (info.projectType === 'single') {
      const calculation = omit(
        info.calculation,
        'id',
        'targetCompanyName',
        'targetCompanyCode',
        'projectCode',
        'projectName',
      );
      info = {
        ...info,
        ...calculation,
      };
    }
    info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
    info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
    pricingForm.value = info;
    nextTick(() => {
      if (pricingForm.value.projectType === 'single') {
        LadderPenaltyRef.value.init(pricingForm.value);
        RepaymentCalculationRef.value.init(pricingForm.value);
      }
    });
  }
};
// 项目信息
const projectInfo = ref({});

const getProjectInfo = async (id: number) => {
  projectInfo.value = await getOverviewInfoApi(id);
};

const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const pricingForm = ref<PricingInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type?: string) => {
  await FormRef.value.validate();
  if (pricingForm.value.projectType === 'single') {
    await RepaymentCalculationRef.value.save();
    await LadderPenaltyRef.value.save();
  }
  loading.submit = true;
  let api = pricingForm.value.projectType === 'comprehensive' ? addComprehensivePricingApi : addSinglePricingApi;
  if (pricingForm.value.id) {
    api = pricingForm.value.projectType === 'comprehensive' ? editComprehensivePricingApi : editSinglePricingApi;
  }
  const formData = cloneDeep(pricingForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  formData.calculation = { ...formData };
  delete formData.detailList;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};
const selectInitiation = async (_value: number, data: InitiationInfo) => {
  pricingForm.value.pricingName = `${data.label}-定价`;
  await getProjectInfo(data.value);
  const {
    projectName,
    projectType,
    receivableAmount,
    targetCompanyName,
    targetCompanyCode,
    creditCalculateAmount,
    creditAmount: pricingCreditAmount,
    creditTerm: pricingCreditTerm,
    creditRate: pricingCreditRate,
    creditType: pricingCreditType,
  } = projectInfo.value;
  pricingForm.value = {
    ...pricingForm.value,
    projectName,
    projectType,
    receivableAmount,
    targetCompanyName,
    targetCompanyCode,
    creditCalculateAmount,
    pricingCreditAmount,
    pricingCreditTerm,
    pricingCreditRate,
    pricingCreditType,
  };
  const creditCompany = projectInfo.value?.companyList.find((item: object) => item.projectCompanyType === 'credit');
  pricingForm.value.creditCompanyCode = creditCompany?.companyCode || '';
  pricingForm.value.creditCompanyName = creditCompany?.companyName || '';
  pricingForm.value.financingAmount = creditCalculateAmount;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  pricingName: [{ required: true, message: '请输入项目定价名称' }],
  customerCategory: [{ required: true, message: '请选择客户类别', trigger: 'change' }],
  paymentCollectionMethod: [{ required: true, message: '请选择客户类别', trigger: 'change' }],
  pricingBasicRatio: [{ required: true, message: '请输入基准定价' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  financingAmount: [{ required: true, message: '请输入计划融资金额' }],
  gracePeriodDays: [{ required: true, message: '请输入宽限期天数' }],
  gracePeriodRate: [{ required: true, message: '请输入宽限期费率' }],
  penaltyInterestRate: [{ required: true, message: '请输入固定罚息利率' }],
  expectedRepayPeriods: [{ required: true, message: '还款期数' }],
  nominalInterestRate: [{ required: true, message: '合同利率' }],
  pricingXirrRate: [{ required: true, message: '综合收益率' }],
  penaltyType: [{ required: true, message: '请选择罚息类型', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目定价信息" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="pricingForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目" name="projectId">
              <ApiComponent
                v-model="pricingForm.projectId as unknown as string"
                :component="Select"
                :api="getProjectListApi"
                :params="{ status: 'EFFECTIVE' }"
                label-field="projectName"
                value-field="id"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目类型" name="projectType">
              <a-select
                v-model:value="pricingForm.projectType"
                :options="dictStore.getDictList('FCT_PROJECT_TYPE')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目定价名称" name="pricingName">
              <a-input v-model:value="pricingForm.pricingName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="pricingForm.projectType === 'comprehensive'">
            <a-form-item label="授信客户" name="creditCompanyName">
              {{ pricingForm.creditCompanyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="pricingForm.projectType === 'single'">
            <a-form-item label="合作客户" name="targetCompanyName">
              {{ pricingForm.targetCompanyName }}
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户类别" name="customerCategory">
              <a-select
                v-model:value="pricingForm.customerCategory"
                :options="dictStore.getDictList('FCT_CUSTOMER_CATEGORY')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户分级" name="customerLevel">
              <a-select
                v-model:value="pricingForm.customerLevel"
                :options="dictStore.getDictList('FCT_CUSTOMER_LEVEL')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="行业属性" name="industryAttributes">
              <a-input v-model:value="pricingForm.industryAttributes" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度测算结果" name="creditCalculateAmount">
              <a-input v-model:value="pricingForm.creditCalculateAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="区域特性" name="areaCharacteristics">
              <a-input v-model:value="pricingForm.areaCharacteristics" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="底层项目" name="bottomProject">
              <a-input v-model:value="pricingForm.bottomProject" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="担保方式" name="guaranteeMethodDesc">
              <a-input v-model:value="pricingForm.guaranteeMethodDesc" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="pricingForm.projectType === 'single'">
            <a-form-item label="应收账款金额（元）">
              {{ pricingForm.financingAmount }}
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="其他情况" name="pricingOtherDesc" v-bind="fullProp">
              <a-textarea v-model:value="pricingForm.pricingOtherDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption
          v-if="pricingForm.projectType"
          :content="`一般定价方案（${dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE')}）`"
        />
        <a-row class="mt-5">
          <template v-if="pricingForm.projectType === 'comprehensive'">
            <a-col v-bind="colSpan">
              <a-form-item label="授信金额（元）" name="pricingCreditAmount">
                <a-input v-model:value="pricingForm.pricingCreditAmount" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信期限（个月）" name="pricingCreditTerm">
                <a-input v-model:value="pricingForm.pricingCreditTerm" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信费率（%）" name="pricingCreditRate">
                <a-input v-model:value="pricingForm.pricingCreditRate" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信额度类型">
                {{ dictStore.formatter(pricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
              </a-form-item>
            </a-col>
          </template>
          <template v-if="pricingForm.projectType">
            <a-col v-bind="colSpan">
              <a-form-item label="基准定价（%/年）" name="pricingBasicRatio">
                <a-input v-model:value="pricingForm.pricingBasicRatio" />
              </a-form-item>
            </a-col>
          </template>
          <template v-if="pricingForm.projectType === 'single'">
            <a-col v-bind="colSpan">
              <a-form-item label="综合收益率（%/年）" name="pricingXirrRate">
                <a-input v-model:value="pricingForm.pricingXirrRate" />
              </a-form-item>
            </a-col>
          </template>
        </a-row>
        <div v-if="pricingForm.projectType === 'single'">
          <BasicCaption content="还本付息方案" />

          <a-row class="mt-5">
            <a-col v-bind="colSpan">
              <a-form-item label="宽限期天数（天）" name="gracePeriodDays">
                <a-input v-model:value="pricingForm.gracePeriodDays" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="宽限期费率（%/年）" name="gracePeriodRate">
                <a-input v-model:value="pricingForm.gracePeriodRate" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="服务费（元）" name="serviceFeeAmount">
                <a-input v-model:value="pricingForm.serviceFeeAmount" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="融资比例（%）" name="financingRatio">
                <a-input v-model:value="pricingForm.financingRatio" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="罚息类型" name="penaltyType">
                <a-radio-group
                  v-model:value="pricingForm.penaltyType"
                  :options="dictStore.getDictList('FCT_PENALTY_TYPE')"
                />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan" v-if="pricingForm.penaltyType === 'fixed'">
              <a-form-item label="固定罚息利率（%）" name="penaltyInterestRate">
                <a-input v-model:value="pricingForm.penaltyInterestRate" />
              </a-form-item>
            </a-col>
          </a-row>
          <LadderPenalty ref="LadderPenaltyRef" v-show="pricingForm.penaltyType === 'ladder'" v-model="pricingForm" />
          <RepaymentCalculation ref="RepaymentCalculationRef" v-model="pricingForm" calculation-type="Pricing" />
        </div>
        <BaseAttachmentList
          v-model="pricingForm.attachmentList"
          :business-id="pricingForm.id"
          business-type="FCT_PROJECT_PRICING"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
