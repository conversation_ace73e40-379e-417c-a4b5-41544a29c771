<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getReceivablePoolPageListApi } from '#/api';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'poolName',
      label: '应收账款池名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '关联项目',
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '池保理融资企业',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'poolName', title: '应收账款池名称', width: 180 },
    { field: 'projectName', title: '关联项目', width: 180 },
    { field: 'companyName', title: '池保理融资企业' },
    { field: 'poolTotalAmount', title: '池内资产总额（元）' },
    { field: 'poolFinanceRate', title: '池融资比例' },
    { field: 'poolValidity', title: '应收账款池期限（个月）' },
    { field: 'poolDueDate', title: '应收账款池到期日', formatter: 'formatDate' },
  ],
  height: 500,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getReceivablePoolPageListApi({
          ...formValues,
          status: 'EFFECTIVE',
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
  onConfirm: async () => {
    const selectedRows = gridApi.grid.getCheckboxRecords();
    const processedRows = selectedRows.map(({ id: _id, ...rest }) => rest);
    modalApi.setData(processedRows);
    await modalApi.close();
  },
});
</script>

<template>
  <Modal title="选择应收账款池" class="w-[80vw]">
    <Grid />
  </Modal>
</template>

<style></style>
