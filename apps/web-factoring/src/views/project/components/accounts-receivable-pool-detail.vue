<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { watch } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const props = defineProps({
  info: { type: Object, default: () => ({}) },
});
const GridOptions = {
  columns: [
    { field: 'poolName', title: '应收账款池名称', width: 180 },
    { field: 'projectName', title: '关联项目', width: 180 },
    { field: 'companyName', title: '池保理融资企业' },
    { field: 'poolTotalAmount', title: '池内资产总额（元）' },
    { field: 'poolFinanceRate', title: '池融资比例' },
    { field: 'poolValidity', title: '应收账款池期限（个月）' },
    { field: 'poolDueDate', title: '应收账款池到期日', formatter: 'formatDate' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: InitiationInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
watch(
  () => props.info,
  (val = {}) => {
    gridApi.grid.reloadData(val.receivableRefList ?? []);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <BasicCaption content="应收账款池信息" />
    <Grid />
  </div>
</template>
