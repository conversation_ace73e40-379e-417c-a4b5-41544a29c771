<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { useVbenModal } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { isEmpty } from '@vben/utils';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import AccountsPoolPopup from './accounts-pool-popup.vue';

const baseForm = defineModel({ type: Object, required: true });
const init = (form: InitiationInfo) => {
  gridApi.grid.reloadData(form.receivableRefList);
};
const GridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'poolName', title: '应收账款池名称', width: 180 },
    { field: 'projectName', title: '关联项目', width: 180 },
    { field: 'companyName', title: '池保理融资企业' },
    { field: 'poolTotalAmount', title: '池内资产总额（元）' },
    { field: 'poolFinanceRate', title: '池融资比例' },
    { field: 'poolValidity', title: '应收账款池期限（个月）' },
    { field: 'poolDueDate', title: '应收账款池到期日', formatter: 'formatDate' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
});
const [Modal, modalApi] = useVbenModal({
  connectedComponent: AccountsPoolPopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      const selectedAccounts = modalApi.getData() || [];
      if (Array.isArray(selectedAccounts)) {
        gridApi.grid.reloadData([...(baseForm.value.receivableRefList || []), ...selectedAccounts]);
      }
    }
  },
});
const del = () => {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.error('请选择数据');
    return false;
  }
  gridApi.grid.removeCheckboxRow(selectedRows);
};
const save = async () => {
  const { visibleData } = gridApi.grid.getTableData();
  if (!isEmpty(visibleData)) {
    visibleData.forEach((item: any) => {
      item.poolDueDate = dayjs(item.poolDueDate).valueOf();
    });
  }
  baseForm.value.receivableRefList = visibleData;
  return baseForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="应收账款池信息" />
    <Grid>
      <template #toolbar-tools>
        <a-space>
          <a-button class="mr-2" type="primary" @click="modalApi.setData({}).open()"> 选择应收账款池 </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
    </Grid>
    <Modal />
  </div>
</template>
